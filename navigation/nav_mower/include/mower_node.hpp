#ifndef NAVIGATION_MOWER_NODE_HPP
#define NAVIGATION_MOWER_NODE_HPP

#include "data_type.hpp"
#include "geometry_msgs/twist__struct.h"
#include "mower.hpp"
#include "mower_config.hpp"
#include "mower_msgs/msg/beacon_status.hpp"
#include "mower_msgs/msg/charge_pile_dock_status.hpp"
#include "mower_msgs/msg/mcu_exception.hpp"
#include "mower_msgs/msg/mcu_imu.hpp"
#include "mower_msgs/msg/mcu_motor_speed.hpp"
#include "mower_msgs/msg/mcu_sensor.hpp"
#include "mower_msgs/msg/slope_detection_result.hpp"
#include "mower_msgs/msg/soc_imu.hpp"
#include "mower_msgs/msg/twist.hpp"
#include "mower_msgs/srv/alg_version.hpp"
#include "mower_msgs/srv/area_calculation.hpp"
#include "mower_msgs/srv/cross_region_result.hpp"
#include "mower_msgs/srv/cut_edge_result.hpp"
#include "mower_msgs/srv/explore_map.hpp"
#include "mower_msgs/srv/explore_map_result.hpp"
#include "mower_msgs/srv/go_charge.hpp"
#include "mower_msgs/srv/go_mow.hpp"
#include "mower_msgs/srv/go_to_cross_region.hpp"
#include "mower_msgs/srv/go_to_cut_edge.hpp"
#include "mower_msgs/srv/go_to_standby.hpp"
#include "mower_msgs/srv/mcu_mission_type.hpp"
#include "mower_msgs/srv/recharge_result.hpp"
#include "mower_msgs/srv/spiral_mow_result.hpp"
#include "mower_msgs/srv/undock_result.hpp"
#include "mower_node_config.hpp"
#include "ob_mower_msgs/charge_mark_detect_result.h"
#include "ob_mower_msgs/localization_motion_detection_result__struct.h"
#include "ob_mower_msgs/mark_location_result_struct.h"
#include "ob_mower_msgs/nav_alg_ctrl__struct.h"
#include "ob_mower_msgs/nav_behavior_state__struct.h"
#include "ob_mower_msgs/nav_cross_region_final_result__struct.h"
#include "ob_mower_msgs/nav_cross_region_state__struct.h"
#include "ob_mower_msgs/nav_fusion_pose.h"
#include "ob_mower_msgs/nav_random_mower_state__struct.h"
#include "ob_mower_msgs/nav_recharge_final_result__struct.h"
#include "ob_mower_msgs/nav_recharge_state__struct.h"
#include "ob_mower_msgs/nav_running_state__struct.h"
#include "ob_mower_msgs/nav_spiral_mower_final_result.h"
#include "ob_mower_msgs/perception_fusion_result_struct.h"
#include "ob_mower_msgs/perception_localization_alg_ctrl.h"
#include "ob_mower_msgs/qrcode_result__struct.h"
#include "ob_mower_srvs/mark_location_detect_mark_id_service__struct.h"
#include "ob_mower_srvs/nav_mower_alg_test_service__struct.h"
#include "ob_mower_srvs/nav_mower_node_param.h"
#include "opencv2/opencv.hpp"
#include "slope_control.hpp"
#include "std_msgs/int32__struct.h"
#include "utils/heartbeat_publisher.hpp"
#include "utils/iceoryx_header.hpp"

#include <chrono>
#include <cmath>
#include <fstream>
#include <memory>
#include <queue>
#include <sys/prctl.h>
#include <thread>

namespace fescue_iox
{

#define STUCK 1

// Filter state
struct FilterState
{
    float accel_x = 0.0f, accel_y = 0.0f, accel_z = 0.0f;
    float gyro_x = 0.0f, gyro_y = 0.0f, gyro_z = 0.0f;
    float motor_speed_left = 0.0f, motor_speed_right = 0.0f;
};

class NavigationMowerNode
{
    using iox_twist_publisher = iox::popo::Publisher<mower_msgs::msg::Twist>;
    using iox_nav_alg_ctrl_publisher = iox::popo::Publisher<fescue_msgs__msg__NavigationAlgoCtrlData>;

    using get_mcu_mission_type_request = mower_msgs::srv::GetMcuMissionTypeRequest;
    using get_mcu_mission_type_response = mower_msgs::srv::GetMcuMissionTypeResponse;

    using get_node_param_request = ob_mower_srvs::GetNavMowerNodeParamRequest;
    using get_node_param_response = ob_mower_srvs::GetNavMowerNodeParamResponse;
    using set_node_param_request = ob_mower_srvs::SetNavMowerNodeParamRequest;
    using set_node_param_response = ob_mower_srvs::SetNavMowerNodeParamResponse;

    using alg_test_request = fescue_msgs__srv__SetNavigationMowerAlgTest_Request;
    using alg_test_response = fescue_msgs__srv__SetNavigationMowerAlgTest_Response;
    using alg_version_request = mower_msgs::srv::AlgVersionRequest;
    using alg_version_response = mower_msgs::srv::AlgVersionResponse;

public:
    NavigationMowerNode(const std::string &node_name);
    ~NavigationMowerNode();

private:
    void InitWorkingDirectory();
    void InitParam();
    void InitLogger();
    void InitSubscriber();
    void InitPublisher();
    void InitService();
    void InitTaskStateByMCUMissionType();
    void InitAlgorithmParam();
    void InitSlopeCtrlAlgorithmParam();
    void InitAlgorithm();
    void DeinitAlgorithm();
    void InitHeartbeat();
    void InitCalibResult();

    void SetMowerRunningState(MowerRunningState state)
    {
        if (save_record_data_.load())
        {
            DealDataFile(state);
        }
        if (mower_alg_)
        {
            mower_alg_->SetMowerRunningState(state);
        }
    }
    void SetMowerVelPublisherProhibit(bool prohibit)
    {
        if (mower_alg_)
        {
            mower_alg_->SetVelPublisherProhibit(prohibit);
        }
    }

    void SetMowerAppTriggersRecharge(bool flag)
    {
        is_recharge_start_ = flag;
        if (mower_alg_)
        {
            mower_alg_->SetAppTriggersRecharge(flag);
        }
    }

    void SetMowerAppTriggersCrossRegion(bool flag)
    {
        is_cross_region_start_ = flag;
        if (mower_alg_)
        {
            mower_alg_->SetAppTriggersCrossRegion(flag);
        }
    }

    void SetMowerAppTriggersMower(bool flag)
    {
        is_random_mower_start_ = flag;
        if (mower_alg_)
        {
            mower_alg_->SetAppTriggersMower(flag);
        }
    }

    void SetMowerAppTriggersSpiralMower(bool flag)
    {
        is_spiral_mower_start_ = flag;
        if (mower_alg_)
        {
            mower_alg_->SetAppTriggersSpiralMower(flag);
        }
    }

    void SetMowerAppTriggersRegionExplore(bool flag)
    {
        is_region_explore_start_ = flag;
        if (mower_alg_)
        {
            mower_alg_->SetAppTriggersRegionExplore(flag);
        }
    }

    void SetMowerAppTriggersCutBorder(bool flag)
    {
        is_cut_border_start_ = flag;
        if (mower_alg_)
        {
            mower_alg_->SetAppTriggersCutBorder(flag);
        }
    }

    void SetMowerComplete(bool flag)
    {
        if (mower_alg_)
        {
            mower_alg_->SetMowerComplete(flag);
        }
    }

private:
    void DealMCUException(const mower_msgs::msg::McuException &data);
    void DealMCUSensor(const mower_msgs::msg::McuSensor &data);
    void DealSlopeResult(const mower_msgs::msg::LawnmowerSlopeStatus &data);
    void DealMowerTwist(const geometry_msgs__msg__Twist_iox &msg);
    void DealBehaviorState(const fescue_msgs__msg__BehaviorStateData &msg);
    void DealCrossRegionState(const fescue_msgs__msg__CrossRegionStateData &msg);
    void DealRechargeState(const fescue_msgs__msg__RechargeStateData &msg);
    void DealMarkLocationResult(const fescue_msgs__msg__MarkLocationResult &msg);
    void DealRandomMowerState(const fescue_msgs__msg__RandomMowerStateData &msg);
    void DealFunctionState(const fescue_msgs_FunctionStateData &msg);
    void CheckMCUExceptionTimeout();
    void CheckBehaviorStateTimeout();
    void CheckTimeouts();
    void DealQRCodeLocationResult(const fescue_msgs__msg__QrCodeResult &msg);
    void DealRechargeFinalResult(const fescue_msgs__msg__NavRechargeFinalResult &msg);
    void DealCrossRegionFinalResult(const fescue_msgs__msg__NavCrossRegionFinalResult &msg);
    void DealChargeDetectResult(const fescue_msgs__msg__ChargeResult &msg);
    void PrintNavAlgCtrlData(const fescue_msgs__msg__NavigationAlgoCtrlData &data);
    void DealOtherTaskNavAlgCtrl(const fescue_msgs__msg__NavigationAlgoCtrlData &data);
    void DealSpiralMowerFinalResult(const ob_mower_msgs::NavSpiralMowerFinalResult &msg);
    void PublishPerceptionLocalizationAlgCtrl(const ob_mower_msgs::PerceptionLocalizationAlgCtrl &msg);
    void PublishMCUTwist(const mower_msgs::msg::Twist &msg);
    void PublishBeaconStatus(mower_msgs::msg::BeaconStatusType status);
    void SetLocalizationAreaEstimateState(ob_mower_msgs::PerceptionLocalizationAlgState state);
    void SetPerceptionAndLocalizationState(ob_mower_msgs::PerceptionLocalizationAlgState state);
    void SetPerceptionAndLocalizationStateOnRecharge();
    void SetPerceptionAndLocalizationStateOnRechargeHaveFindQRCode();
    void SetPerceptionAndLocalizationStateOnRechargeFindingQRCode();
    void SetPerceptionAndLocalizationStateOnRandomMower();
    void SetPerceptionAndLocalizationStateOnCrossRegion();
    void SetPerceptionAndLocalizationStateOnExploreMap();
    void SetPerceptionAndLocalizationStateOnCutBorder();
    void SetPerceptionAndLocalizationStateOnSpiralMower();
    bool GetMCUMissionType(mower_msgs::srv::MowerMissionType &mission_type);
    bool SendAlgorithmVersion();
    void DealPerceptionFusionResult(const fescue_msgs__msg__PerceptionFusionResult &msg);
    void DealMcuImu(const mower_msgs::msg::McuImu &data);
    void DealSocImu(const mower_msgs::msg::SocImu &data);
    void DealMcuMotorSpeed(const mower_msgs::msg::McuMotorSpeed &data);
    void DealMotionDetectionResult(const fescue_msgs__msg__LocalizationMotionDetectionResult &data);
    void DealSlopeDetectionResult(const mower_msgs::msg::LawnmowerSlopeStatus &data);

    void MowerThread();
    void DealFeatureSelectCallback(const fescue_msgs__msg__NavigationAlgoCtrlData &data);
    bool DealMarkLocationMarkIdCallback(int mark_id);
    bool SendCrossRegionFinalResult(bool result);
    bool SendRechargeFinalResult(bool recharge_completed, bool recharge_result, mower_msgs::srv::RechargeOperationStatus status = mower_msgs::srv::RechargeOperationStatus::INTERRUPTIBLE);
    bool SendUndockFinalResult(bool undock_completed, bool undock_result, mower_msgs::srv::UndockOperationStatus status);
    bool SendSpiralMowerFinalResult(bool spiral_completed, bool spiral_result);
    bool SendRegionExporeFinalResult(const RegionExploreResult &result);
    bool SendCutBorderFinalResult(bool result);
    bool DealAreaCalcStart(uint64_t timestamp_ms);
    bool DealAreaCalcStop(uint64_t timestamp_ms, float &area, float &perimeter);
    void DealUndockFinalResult(bool completed, bool result, mower_msgs::srv::UndockOperationStatus status);
    bool DealRegionExploreAreaCalcStart(uint64_t timestamp);
    bool DealRegionExploreAreaCalcStop(uint64_t timestamp, float &area, float &perimeter);
    void DealRegionExploreResult(RegionExploreResult &result);
    void DealCutBorderResult(bool completed, bool result);
    void DealCrossRegionRunningStateCallback(CrossRegionRunningState state);
    void DealEdgeFollowStatusCallback(int status);
    bool DealSWMowerRequest(const mower_msgs::srv::GoMowRequestType &data);
    bool DealSWGoCrossRegionRequest(const mower_msgs::srv::GoToCrossRegionRequestType &data);
    bool DealSWRegionExploreRequest(const mower_msgs::srv::ExploreMapRequestType &data);
    bool DealSWCutBorderRequest(const mower_msgs::srv::GoToCutEdgeRequestType &data);
    bool DealSWStandByRequest(const mower_msgs::srv::GoToStandbyRequestType &data);
    bool DealSWChargeRequest(const mower_msgs::srv::GoChargeRequestType &data);
    void VelociyAdjustment(float motor_speed_left, float motor_speed_right,
                           float expected_linear, float expected_angular,
                           float &actual_linear, float &actual_angular,
                           float &adjusted_linear, float &adjusted_angular);
    void PublishMowerTwist(const std::string &sender, float linear, float angular);
    void PublishRunningState(MowerRunningState state);
    void PublishException(mower_msgs::msg::SocExceptionLevel level, mower_msgs::msg::SocExceptionValue value);
    void AlgorithmParamToConfigParam(const MowerAlgParam &param, NavigationMowerAlgConfig &config);
    void ConfigParamToAlgorithmParam(const NavigationMowerAlgConfig &config, MowerAlgParam &param);
    void CloseAllTask();
    bool SetMowerNodeParam(const ob_mower_srvs::NavMowerNodeParamData &data);
    bool GetMowerNodeParam(ob_mower_srvs::NavMowerNodeParamData &data);
    float LowPassFilter(float new_value, float prev_value, float alpha);
    void SetMotorSpeedData(const MotorSpeedData &motor_speed_data);
    void SetMotionDetectionResult(const MotionDetectionResult &motion_detection_result);
    void SetImuData(const ImuData &imu_data);
    void UpdateStuckDetectionState(float linear_velocity, float angular_velocity);

    void OpenImuDataFile();
    void CloseImuDataFile();
    void WriteImuDataFile(const mower_msgs::msg::SocImu &data);
    void WriteImuDataFile(const mower_msgs::msg::McuImu &data);
    void PublishFusionPose();
    void RecordData();
    void DealDataFile(const MowerRunningState &state);
    bool ConvertFeatureSelectToNavAlgCtrlData(const std::vector<FeatureSelectData> &data, fescue_msgs__msg__NavigationAlgoCtrlData &ctrl_data);
    void PublishDangerousPointCloud();

private:
    // Subscribers
    std::unique_ptr<IceoryxSubscriberMower<fescue_msgs__msg__MarkLocationResult>> sub_mark_loc_result_{nullptr};
    std::unique_ptr<IceoryxSubscriberMower<fescue_msgs__msg__CrossRegionStateData>> sub_cross_region_state_{nullptr};
    std::unique_ptr<IceoryxSubscriberMower<fescue_msgs__msg__CrossRegionStateData>> sub_cross_region_state_mower_{nullptr};
    std::unique_ptr<IceoryxSubscriberMower<fescue_msgs__msg__RechargeStateData>> sub_recharge_state_{nullptr};
    std::unique_ptr<IceoryxSubscriberMower<fescue_msgs__msg__RandomMowerStateData>> sub_random_mower_state_{nullptr};
    std::unique_ptr<IceoryxSubscriberMower<fescue_msgs__msg__BehaviorStateData>> sub_behavior_state_{nullptr};
    std::unique_ptr<IceoryxSubscriberMower<fescue_msgs__msg__PerceptionFusionResult>> sub_perception_fusion_result_{nullptr};
    std::unique_ptr<IceoryxSubscriberMower<geometry_msgs__msg__Twist_iox>> sub_nav_twist_{nullptr};
    std::unique_ptr<IceoryxSubscriberMower<mower_msgs::msg::McuSensor>> sub_mcu_sensor_{nullptr};
    std::unique_ptr<IceoryxSubscriberMower<mower_msgs::msg::McuException>> sub_mcu_exception_{nullptr};
    std::unique_ptr<IceoryxSubscriberMower<fescue_msgs__msg__QrCodeResult>> sub_qrcode_loc_result_{nullptr};
    std::unique_ptr<IceoryxSubscriberMower<fescue_msgs__msg__NavRechargeFinalResult>> sub_recharge_final_result_{nullptr};
    std::unique_ptr<IceoryxSubscriberMower<fescue_msgs__msg__NavCrossRegionFinalResult>> sub_cross_region_final_result_{nullptr};
    std::unique_ptr<IceoryxSubscriberMower<ob_mower_msgs::NavSpiralMowerFinalResult>> sub_spiral_mower_final_result_{nullptr};
    std::unique_ptr<IceoryxSubscriberMower<mower_msgs::msg::McuImu>> sub_mcu_imu_{nullptr};
    std::unique_ptr<IceoryxSubscriberMower<mower_msgs::msg::SocImu>> sub_soc_imu_{nullptr};
    std::unique_ptr<IceoryxSubscriberMower<mower_msgs::msg::McuMotorSpeed>> sub_mcu_motor_speed_{nullptr};
    std::unique_ptr<IceoryxSubscriberMower<fescue_msgs__msg__LocalizationMotionDetectionResult>> sub_motion_detection_result_{nullptr};
    std::unique_ptr<IceoryxSubscriberMower<mower_msgs::msg::LawnmowerSlopeStatus>> sub_loc_slope_detection_result_{nullptr};
    std::unique_ptr<IceoryxSubscriberMower<fescue_msgs__msg__ChargeMarkDetectResult>> sub_charge_mark_detect_result_{nullptr};
    std::unique_ptr<IceoryxSubscriberMower<fescue_msgs_FunctionStateData>> sub_function_state_{nullptr};
    std::unique_ptr<IceoryxSubscriberMower<fescue_msgs__msg__NavigationAlgoCtrlData>> sub_random_mower_nav_alg_ctrl_{nullptr};
    std::unique_ptr<IceoryxSubscriberMower<fescue_msgs__msg__NavigationAlgoCtrlData>> sub_edge_follow_nav_alg_ctrl_{nullptr};
    std::unique_ptr<IceoryxSubscriberMower<fescue_msgs__msg__NavigationAlgoCtrlData>> sub_cross_region_nav_alg_ctrl_{nullptr};

    // Publishers
    std::unique_ptr<IceoryxPublisherMower<fescue_msgs__msg__CrossRegionStateData>> pub_cross_region_state_mower_{nullptr};
    std::unique_ptr<IceoryxPublisherMower<fescue_msgs__msg__NavigationAlgoCtrlData>> pub_nav_alg_ctrl_{nullptr};
    std::unique_ptr<IceoryxPublisherMower<mower_msgs::msg::Twist>> pub_nav_twist_{nullptr};
    std::unique_ptr<IceoryxPublisherMower<fescue_msgs__msg__NavigationRunningStateData>> pub_nav_running_state_{nullptr};
    std::unique_ptr<IceoryxPublisherMower<ob_mower_msgs::PerceptionLocalizationAlgCtrl>> pub_perception_localization_alg_ctrl_{nullptr};
    std::unique_ptr<IceoryxPublisherMower<mower_msgs::msg::BeaconStatus>> pub_beacon_status_{nullptr};
    std::unique_ptr<IceoryxPublisherMower<ob_mower_msgs::NavFusionPose>> pub_nav_fusion_pose_{nullptr};
    std::unique_ptr<IceoryxPublisherMower<std_msgs__msg__Int32_iox>> pub_edge_follow_running_status_{nullptr};
    std::unique_ptr<IceoryxPublisherMower<ob_mower_msgs::NavPointCloud>> pub_nav_dangerous_point_cloud_{nullptr};

    // Services
    std::unique_ptr<IceoryxServerMower<get_node_param_request, get_node_param_response>> service_get_node_param_{nullptr};
    std::unique_ptr<IceoryxServerMower<set_node_param_request, set_node_param_response>> service_set_node_param_{nullptr};
    std::unique_ptr<IceoryxServerPlanning<mower_msgs::srv::GoMowRequest, mower_msgs::srv::GoMowResponse>> service_sw_go_mower_{nullptr};
    std::unique_ptr<IceoryxServerPlanning<mower_msgs::srv::GoChargeRequest, mower_msgs::srv::GoChargeResponse>> service_sw_go_charge_{nullptr};
    std::unique_ptr<IceoryxServerPlanning<mower_msgs::srv::GoToCrossRegionRequest, mower_msgs::srv::GoToCrossRegionResponse>> service_sw_go_cross_region_{nullptr};
    std::unique_ptr<IceoryxServerPlanning<mower_msgs::srv::GoToStandbyRequest, mower_msgs::srv::GoToStandbyResponse>> service_sw_go_standby_{nullptr};
    std::unique_ptr<IceoryxServerPlanning<mower_msgs::srv::ExploreMapRequest, mower_msgs::srv::ExploreMapResponse>> service_sw_explore_map_{nullptr};
    std::unique_ptr<IceoryxServerPlanning<mower_msgs::srv::GoToCutEdgeRequest, mower_msgs::srv::GoToCutEdgeResponse>> service_sw_go_cut_edge_{nullptr};

private:
    FilterState filter_state_;
    float alpha_imu_ = 0.1f;   // IMU filter coefficient
    float alpha_speed_ = 0.2f; // Rotation speed filter coefficient
    bool enable_imu_filter_ = true;
    bool enable_motor_speed_filter_ = false;

private:
    std::atomic_bool mower_enable_{true};
    std::atomic_bool thread_running_{true};
    std::atomic_bool save_record_data_{false};
    std::atomic_bool save_imu_data_{false};
    std::thread mower_thread_;

    bool is_recharge_start_{false};       // Whether the return to charge task has started
    bool is_cross_region_start_{false};   // Whether the cross-region task has started
    bool is_random_mower_start_{false};   // Whether the mowing task has started
    bool is_region_explore_start_{false}; // Whether the map exploration task has started
    bool is_spiral_mower_start_{false};
    bool is_cut_border_start_{false};

    std::mutex mark_loc_mtx_;
    std::mutex cross_region_mtx_;
    std::mutex qrcode_loc_mtx_;
    std::mutex fusion_mutex_;
    std::mutex recharge_mtx_;
    std::mutex behavior_mtx_;

    std::mutex imu_mtx_;
    ImuData imu_data_;

    std::mutex raw_imu_mtx_;
    ImuData raw_imu_data_;
    std::mutex motor_speed_mtx_;
    MotorSpeedData motor_speed_data_;

    bool is_vel_adjustment_{false}; // Whether to adjust the speed
    float wheel_radius_{0.1f};      // Wheel radius (meters)
    float wheel_base_{0.335f};      // Wheelbase (meters)
    float adjusted_vel_kp_{0.3f};   // Proportional gain, needs to be adjusted based on testing
    float vel_limit_param_{1.5f};   // Speed limit parameter, needs to be adjusted based on testing
    mower_msgs::msg::LawnmowerSlopeStatus slope_result_;

    std::mutex motion_detection_result_mtx_;
    MotionDetectionResult motion_detection_result_;

    std::mutex slope_detection_result_mtx_;
    SlopeDetectionResult slope_detection_result_;

    McuExceptionStatus mcu_exception_status_{McuExceptionStatus::NORMAL};
    std::mutex mcu_exception_mutex_;

    std::mutex charge_pile_dock_status_mtx_;
    bool charge_pile_dock_status_{false}; // Docking status with the charging pile

    std::mutex station_mtx_;
    ChargeStationDetectResult charge_station_result_;

    MarkLocationResult mark_loc_result_;
    std::atomic<CrossRegionRunningState> cross_region_state_{CrossRegionRunningState::UNDEFINED};
    std::atomic<CrossRegionRunningState> last_cross_region_state_{CrossRegionRunningState::UNDEFINED};
    std::atomic<RandomMowerRunningState> random_mower_state_{RandomMowerRunningState::NORMAL};
    std::atomic<RechargeRunningState> recharge_state_{RechargeRunningState::UNDEFINED};
    std::atomic<RechargeRunningState> last_recharge_state_{RechargeRunningState::UNDEFINED};
    MowerRunningState mower_state_{MowerRunningState::STOP};
    BehaviorRunningState behavior_state_{BehaviorRunningState::UNDEFINED};
    bool is_behavior_loop_{false};
    std::vector<BehaviorExceptionType> triggered_exception_types_;

    QRCodeLocationResult qrcode_loc_result_;
    PerceptionFusionResult fusion_result_;

    std::mutex nav_ctrl_mutex_;
    MowerAlgParam mower_alg_param_;
    std::unique_ptr<NavigationMowerAlg> mower_alg_{nullptr};

    // Added
    std::chrono::steady_clock::time_point last_mcu_exception_time_;
    std::chrono::steady_clock::time_point last_behavior_timeout_time_;

    // Stuck detection state management
    bool stuck_detection_should_be_active_{false};                            // The current state that stuck detection should be in
    bool stuck_detection_current_active_{false};                              // The current actual state of stuck detection
    std::chrono::steady_clock::time_point stuck_detection_state_change_time_; // Time when the state change started
    float stuck_detection_start_threshold_seconds_{30.0f};                    // Time threshold (seconds) required to start stuck detection
    float stuck_detection_stop_threshold_seconds_{30.0f};                     // Time threshold (seconds) required to pause stuck detection

    std::unique_ptr<NodeHeartbeatPublisher> pub_heartbeat_{nullptr};
    std::unique_ptr<NavigationSlopeControlAlg> slope_ctrl_alg_{nullptr};
    SlopeControlAlgParam slope_ctrl_alg_param_;
    bool slope_ctrl_enable_{false};

private:
    std::string node_name_{"navigation_mower_node"};
    std::string log_dir_{"/userdata/log"};
    std::string console_log_level_{"info"};
    std::string file_log_level_{"warn"};
    std::string chassis_type_{"gelibo"};
    std::string mower_alg_conf_file_{"conf/navigation_mower_node/mower.yaml"};
    std::string slope_ctrl_alg_conf_file_{"conf/navigation_mower_node/slope_control.yaml"};

    std::ofstream imu_data_file_;
    std::ofstream data_file_;
    std::mutex data_file_mutex_;
    bool is_imu_calibration_recorded_{false};
    std::vector<std::string> buffer_data_;
    MowerRunningState last_mower_state_{MowerRunningState::UNDEFINED};
    ob_mower_msgs::NavFusionPose fusion_pose_;
    std::mutex velocity_data_mtx_;
    mower_msgs::msg::Twist velocity_data_;
    uint64_t last_create_file_time_{0};
    std::atomic_bool is_new_fusion_result_{false};
    uint64_t last_log_pose_time_{0};

    fescue_msgs__msg__NavigationAlgoCtrlData last_ctrl_data_;

    std::atomic_bool is_new_fusion_result_record_{false};
    std::atomic_bool is_new_fusion_{false};
    DangerousPointCloud dangerous_point_cloud_;
};

} // namespace fescue_iox

#endif
