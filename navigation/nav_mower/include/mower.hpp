#ifndef NAVIGATION_MOWER_HPP
#define NAVIGATION_MOWER_HPP

#include "data_type.hpp"
#include "iceoryx_publisher_mower.hpp"
#include "imu_data_processor.hpp"
#include "mower_msgs/msg/mcu_sensor.hpp"
#include "mower_msgs/msg/soc_exception.hpp"
#include "mower_msgs/srv/explore_map_result.hpp"
#include "mower_msgs/srv/undock_result.hpp"
#include "ob_mower_msgs/grass_region_result_struct.h"
#include "ob_mower_msgs/nav_function_state.h"
#include "ob_mower_msgs/nav_fusion_pose.h"
#include "ob_mower_msgs/nav_point_cloud.h"
#include "ob_mower_msgs/perception_localization_alg_ctrl.h"
#include "obstacle_classification.hpp"
#include "pose_stuck_detector.hpp"
#include "rolling_grid_map.hpp"
#include "slope_control.hpp"
#include "stuck_detection_recovery.hpp"
#include "utils/pose_fusion.hpp"
#include "velocity_publisher.hpp"

#include <atomic>
#include <chrono>
#include <cmath>
#include <condition_variable>
#include <functional>
#include <future>
#include <memory>
#include <mutex>
#include <queue>
#include <sys/prctl.h>
#include <thread>
#include <unordered_map>

namespace fescue_iox
{

// #define STUCK 1

// Data time information structure for input data validation
struct DataTimeInfo
{
    // Received timestamp in microseconds
    uint64_t recv_timestamp = 0;
    // Send timestamp in microseconds
    uint64_t send_timestamp = 0;
    // Whether data frequency is too low
    bool is_low_freq = false;
    uint32_t low_freq_count = 0;
    // Whether data is timeout
    bool is_timeout = false;
};

struct MowerAlgParam
{
    // Beacon Scheduling - Unstake
    bool is_enable_unstake_mode{true}; // Whether to enable unstake mode /*param*/
    float unstake_distance{1.0};       // Unstake distance /*param*/
    float unstake_adjust_yaw{0.52};    // 0.52（30） Unstake adjust yaw /*param*/
    float unstake_vel_linear{0.2};     // Unstake linear velocity /*param*/
    float unstake_vel_angular{0.5};    // Unstake angular velocity /*param*/

    // Algorithm parameters
    float mower_linear{0.15}; /*param*/
    float mower_angular{0.5}; /*param*/
    // int perception_drive_cooldown_time{5};   // Perception drive cooldown time 5s /*param*/
    int edge_mode_direction{-1};             // Default counterclockwise -1 /*param*/
    float cross_region_adjust_yaw{1.57};     // Adjust yaw after crossing region /*param*/
    float cross_region_adjust_displace{0.3}; // Adjust displacement after crossing region /*param*/
    float mark_distance_threshold{0.5};      // 1.0/0.5 Distance threshold between beacon and mower camera to determine if in region /*param*/
    float camera_2_center_dis{0.37};         // Distance from mower camera to rotation center: Chelsea(0.37) Greebo(0.45) /*param*/

    // New parameters
    int edge_perception_drive_cooldown_time_threshold{10}; // 10s Edge perception drive cooldown time /*param*/
    int qr_detection_cooldown_time_threshold{30};          // 60s Edge perception drive cooldown time /*param*/
    int mark_detection_cooldown_time_threshold{30};        // 60s Edge perception drive cooldown time /*param*/

    // Region exploration
    float recharge_distance_threshold{1.2}; // Region exploration recharge distance threshold/*param*/

    // Pre-mowing processing
    float mower_start_qr_distance_threshold{0.5}; // Distance threshold between mower and QR code before mowing/*param*/

    // Stuck detection data logging control
    bool enable_stuck_detection_data_logging{false}; // Whether to enable stuck detection data logging /*param*/

    // test
    float test_linear_speed{0.0};
    float test_angular_speed{0.5};
    uint64_t test_duration_ms{6280};
};

// Region exploration result
struct RegionExploreResult
{
    bool result;        // Region exploration result
    uint64_t timestamp; // Timestamp, milliseconds
    mower_msgs::srv::MapResult master_region_map_result;
    mower_msgs::srv::MapResult slave_region_map_result;
};

struct MowerAlgResult
{
    bool mower_completed{false};
    MowerAlgResult() = default;
    MowerAlgResult(bool mower_completed)
        : mower_completed(mower_completed)
    {
    }
};

struct FusionData
{
    double time = 0;
    ImuData imu_data;
    MotorSpeedData motor_speed_data;
    MotionDetectionResult motion_detection_result;
    SlopeDetectionResult slope_detection_result;
    AccelerationFilterData acceleration_filter_data;
};

// Anti-jitter mechanism for perception-based adjustment
enum class PerceptionMovementState
{
    IDLE = 0,    // 空闲状态
    FORWARD = 1, // 前进状态
    BACKWARD = 2 // 后退状态
};

struct DangerousPointCloud
{
    bool is_new{false};
    std::vector<Point2f> point_cloud;
    Pose2f pose;
};

class NavigationMowerAlg
{
    struct ExceptionInfo
    {
        RechargeRunningState recharge_state = RechargeRunningState::UNDEFINED;
        CrossRegionRunningState cross_region_state = CrossRegionRunningState::UNDEFINED;
        bool is_slipping = false;
        McuExceptionStatus mcu_exception_status = McuExceptionStatus::UNKNOWN;
        bool is_stuck = false;
    };

    using iox_exception_publisher = iox::popo::Publisher<mower_msgs::msg::SocException>;

public:
    NavigationMowerAlg(const MowerAlgParam &param);
    ~NavigationMowerAlg();
    void ProhibitVelPublisher();
    const char *GetVersion();
    void DataConversion(MarkLocationResult &mark_loc_result);
    MowerAlgResult Run(const MarkLocationResult &mark_loc_result,
                       CrossRegionRunningState cross_region_state,
                       const QRCodeLocationResult &qrcode_loc_result,
                       const PerceptionFusionResult &fusion_result,
                       bool is_new_fusion,
                       RechargeRunningState recharge_state,
                       McuExceptionStatus &mcu_exception_status,
                       BehaviorRunningState &behavior_state,
                       bool is_behavior_loop,
                       const std::vector<BehaviorExceptionType> &triggered_exception_types,
                       RandomMowerRunningState &random_mower_state,
                       const ChargeStationDetectResult &station_result);

    void HandleSelfCheckAndOperation(const MarkLocationResult &mark_loc_result,
                                     CrossRegionRunningState cross_region_state,
                                     const QRCodeLocationResult &qrcode_loc_result,
                                     const PerceptionFusionResult &fusion_result,
                                     RechargeRunningState recharge_state,
                                     McuExceptionStatus &mcu_exception_status,
                                     BehaviorRunningState &behavior_state,
                                     bool is_behavior_loop,
                                     const std::vector<BehaviorExceptionType> &triggered_exception_types,
                                     const ChargeStationDetectResult &station_result);

    void NormalOperation(const MarkLocationResult &mark_loc_result,
                         CrossRegionRunningState cross_region_state,
                         const QRCodeLocationResult &qrcode_loc_result,
                         const PerceptionFusionResult &fusion_result,
                         RechargeRunningState recharge_state,
                         McuExceptionStatus &mcu_exception_status,
                         BehaviorRunningState &behavior_state,
                         bool is_behavior_loop,
                         const std::vector<BehaviorExceptionType> &triggered_exception_types,
                         const ChargeStationDetectResult &station_result);

    void NormalMowingModule(const MarkLocationResult &mark_loc_result,
                            CrossRegionRunningState cross_region_state,
                            const QRCodeLocationResult &qrcode_loc_result,
                            const PerceptionFusionResult &fusion_result,
                            RechargeRunningState recharge_state,
                            McuExceptionStatus &mcu_exception_status,
                            BehaviorRunningState &behavior_state,
                            bool is_behavior_loop,
                            const std::vector<BehaviorExceptionType> &triggered_exception_types,
                            const ChargeStationDetectResult &station_result);

    void RegionExplorationModule(const MarkLocationResult &mark_loc_result,
                                 CrossRegionRunningState cross_region_state,
                                 const QRCodeLocationResult &qrcode_loc_result,
                                 const PerceptionFusionResult &fusion_result,
                                 RechargeRunningState recharge_state,
                                 McuExceptionStatus &mcu_exception_status,
                                 BehaviorRunningState &behavior_state,
                                 bool is_behavior_loop,
                                 const std::vector<BehaviorExceptionType> &triggered_exception_types);

    void CutBorderModule(const MarkLocationResult &mark_loc_result,
                         CrossRegionRunningState cross_region_state,
                         const QRCodeLocationResult &qrcode_loc_result,
                         const PerceptionFusionResult &fusion_result,
                         RechargeRunningState recharge_state,
                         McuExceptionStatus &mcu_exception_status,
                         BehaviorRunningState &behavior_state,
                         bool is_behavior_loop, 
                         const std::vector<BehaviorExceptionType> &triggered_exception_types);

    /**Region exploration function processing function*/
    void PerformExploration(const MarkLocationResult &mark_loc_result, const QRCodeLocationResult &qrcode_loc_result,
                            const PerceptionFusionResult &fusion_result,
                            CrossRegionRunningState &cross_region_state, BehaviorRunningState &behavior_state,
                            bool is_behavior_loop, const std::vector<BehaviorExceptionType> &triggered_exception_types);
    void ProcessExplorationUnstakeMode();
    void ProcessExplorationUnstakeMode(const QRCodeLocationResult &qrcode_loc_result);
    void ProcessBeaconDetection(const MarkLocationResult &mark_loc_result,
                                const PerceptionFusionResult &fusion_result,
                                bool &enter_multi_region_exploration,
                                bool &is_beacon_valid);
    void ProcessSingleAreaExplorationMode(const QRCodeLocationResult &qrcode_loc_result,
                                          const bool &enter_multi_region_exploration);
    void ProcessMultiAreaExplorationMode(const MarkLocationResult &mark_loc_result,
                                         const PerceptionFusionResult &fusion_result,
                                         const bool &enter_multi_region_exploration,
                                         bool &is_beacon_valid);
    void HandleExplorationMcuException(const ExceptionInfo &exception_info);
    void ProcessExplorationRechargeException(RechargeRunningState recharge_state);
    void ProcessExplorationCrossRegionException(CrossRegionRunningState cross_region_state);

    void HandleEdgePerceptionBeaconDetection(const MarkLocationResult &mark_loc_result, const PerceptionFusionResult &fusion_result, bool &is_cooldown_active);
    void HandleEdgeCooldownMechanism(const MarkLocationResult &mark_loc_result, const PerceptionFusionResult &fusion_result, bool &is_cooldown_active,
                                     std::chrono::seconds &perception_drive_duration, int &perception_drive_cooldown_time_threshold);
    void ResetAndActivateCooldown();
    void ShowExplorePrint(BehaviorRunningState &behavior_state);
    int PairNumber(int n);
    void ProcessingExplorationRecharge(const QRCodeLocationResult &qrcode_loc_result);
    /**Region exploration function processing function*/

    void HandleMcuException(const ExceptionInfo &exception_info);
    void ProcessRechargeException(RechargeRunningState recharge_state);
    void ProcessCrossRegionException(CrossRegionRunningState cross_region_state);
    void ProcessRecoveryException(bool is_slipping, McuExceptionStatus mcu_exception_status, bool is_stuck);
    void HandleNormalOperation(CrossRegionRunningState cross_region_state, RechargeRunningState recharge_state,
                               BehaviorRunningState &behavior_state, const QRCodeLocationResult &qrcode_loc_result,
                               const MarkLocationResult &mark_loc_result, const PerceptionFusionResult &fusion_result,
                               const ChargeStationDetectResult &station_result,
                               bool is_behavior_loop, const std::vector<BehaviorExceptionType> &triggered_exception_types);
    void ProcessNormalOperationUnstakeMode();
    void ProcessNormalOperationUnstakeMode(const QRCodeLocationResult &qrcode_loc_result);
    void ProcessRandomMowing();
    void ProcessSpiralMowing();
    void ProcessCrossRegionMode(CrossRegionRunningState cross_region_state);
    void ProcessRechargeMode(RechargeRunningState recharge_state, CrossRegionRunningState cross_region_state,
                             const QRCodeLocationResult &qrcode_loc_result, const MarkLocationResult &mark_loc_result,
                             const PerceptionFusionResult &fusion_result,
                             const ChargeStationDetectResult &station_result);
    void PerformOtherRecharge(const MarkLocationResult &mark_loc_result, CrossRegionRunningState cross_region_state,
                              const QRCodeLocationResult &qrcode_loc_result, const PerceptionFusionResult &fusion_result,
                              const ChargeStationDetectResult &station_result);
    void RechargeBeaconDetection(const MarkLocationResult &mark_loc_result, const PerceptionFusionResult &fusion_result, bool &is_beacon_valid);
    void HandleRechargeCrossRegionStates(CrossRegionRunningState &cross_region_state);

    void ResetMowerAlgFlags();
    void SetMowerAlgParam(const MowerAlgParam &param);

    void SetMowerRunningState(MowerRunningState state);
    void SetFeatureSelectCallback(std::function<void(const std::vector<FeatureSelectData> &)> callback);
    void SetMarkLocationMarkIdCallback(std::function<bool(int)> callback);
    void SetCrossRegionRunningStateCallback(std::function<void(CrossRegionRunningState)> callback);
    void SetRechargeRunningStateCallback(std::function<void(RechargeRunningState)> callback);
    void SetUndockResultCallback(std::function<void(bool, bool, mower_msgs::srv::UndockOperationStatus)> callback);
    void SetAreaCalcStartCallback(std::function<bool(uint64_t)> callback);
    void SetAreaCalcStopCallback(std::function<bool(uint64_t, float &, float &)> callback);
    void SetRegionExploreResultCallback(std::function<void(RegionExploreResult &)> callback);
    void SetCutBorderResultCallback(std::function<void(bool, bool)> callback);
    void SetPerceptionLocalizationAlgCtrlCallback(std::function<void(const ob_mower_msgs::PerceptionLocalizationAlgCtrl &)> callback);
    void SetEdgeFollowStatusCallback(std::function<void(int)> callback);
    void SetAppTriggersRecharge(bool is_recharge);
    void SetAppTriggersCrossRegion(bool is_cross_region);
    void SetAppTriggersCutBorder(bool is_cut_border);
    void SetIsOnDocker(bool is_on_docker) { is_on_docker_ = is_on_docker; }
    void SetMCUSensor(const mower_msgs::msg::McuSensor &data);
    void SetQRCodeLocationResult(const QRCodeLocationResult &qrcode_loc_result);
    void SetVelPublisherProhibit(bool prohibit)
    {
        if (vel_publisher_)
        {
            vel_publisher_->SetProhibitFlag(prohibit);
        }
    }

    void SetChargePileDockStatus(bool status);
    void SetMowerComplete(const bool &mower_completed);
    void SetAppTriggersMower(bool is_mower);
    void SetAppTriggersSpiralMower(bool is_mower);
    void SetAppTriggersRegionExplore(bool is_region_explore);
    void SetGrassDetecteStatus(const GrassDetectStatus &data);
    void SetAllTaskClose();
    void SetFunctionState(const fescue_msgs_enum_FunctionState &function_state);

    void TestNonBlockingControl();

    void SetMotorSpeedData(const MotorSpeedData &motor_speed_data);
    void SetMotionDetectionResult(const MotionDetectionResult &motion_detection_result);
    void SetImuData(const ImuData &imu_data);
    void SetSlopeDetectionResult(const SlopeDetectionResult &slope_detection_result);
    void SetRawImuData(const ImuData &raw_imu_data);
    bool IsWheelSlipping(const MotorSpeedData &motor_data,
                         const MotionDetectionResult &motion_detection_result,
                         float wheel_radius, float wheel_base);
    void PublishException(mower_msgs::msg::SocExceptionLevel level, mower_msgs::msg::SocExceptionValue value);

    bool IsStuckDetected();
    void SetStuckDetectionActive(bool active);

    void SetResetAllStuckStates();

    ob_mower_msgs::NavFusionPose GetFusionPose() const;
    bool GetIsPoseSlip() const { return is_pose_slip_.load(); }
    void SetImuCalibrationConfig(const IMUCalibrationConfig &config);
    const IMUCalibrationConfig &GetImuCalibrationConfig() const { return imu_calibration_config_; }
    bool IsImuCalibrationValid() const { return is_imu_calibration_valid_; }
    const FusionData &GetFusionData() const { return fusion_data_; }
    DangerousPointCloud GetDangerousPointCloud();

private:
    void InitStuckDetectionRecovery();
    void DeinitStuckDetectionRecovery();

    bool StartStuckRecovery();
    void StopStuckRecovery();
    bool IsStuckRecoveryActive();
    bool ShouldPerformStuckDetection();

private:
    void InitPublisher();
    void PublishSlipException(mower_msgs::msg::SocExceptionLevel level, mower_msgs::msg::SocExceptionValue value);
    void PublishVelocity(float linear, float angular, uint64_t duration_ms = 0);
    void PublishZeroVelocity();
    void PauseVelocity();
    void ResumeVelocity();

    void ShowMowerRunningInfo(const MarkLocationResult &mark_loc_result);
    void CheckVelocityAndUpdateState(BehaviorRunningState &behavior_state, bool is_behavior_loop, const std::vector<BehaviorExceptionType> &triggered_exception_types);

    void UpdateFeatureSelection(const ThreadControl &thread_control, std::vector<BehaviorExceptionType> behavior_exception_types = {});
    void DealFeatureSelect(ThreadControl control, bool state);
    void EdgeFollowDisable();
    void EdgeFollowEnable();
    void CrossRegionDisable();
    void CrossRegionEnable();

    void UpdateCrossRegionRunningState(CrossRegionRunningState state);

    void SetUndockResult(bool completed, bool result, mower_msgs::srv::UndockOperationStatus status = mower_msgs::srv::UndockOperationStatus::INTERRUPTIBLE);

    void PerceptionBasedAdjustment(const MarkLocationResult &mark_loc_result, const PerceptionFusionResult &fusion_result);
    void HandlePerceptionBasedForwardBackwardMovement(const PerceptionFusionResult &fusion_result);

    void FingVaidBeaconIdx(const std::vector<MarkIdDistance> &mark_id_distance_vec, int &shortest_dis_inx);

    bool SetMarkLocationMarkId(int mark_id);

    void ControlRotaryMotion(const float &yaw_des, const float &yaw_first, const float &vel_angular);

    void ControlLinearMotion(const float &pass_point, const float &location, const float &vel_linear, const int &reverse);

    // IMU-based control functions
    void ControlRotaryMotionWithIMU(const float &yaw_des, const float &yaw_first, const float &vel_angular);
    void ControlLinearMotionWithIMUThread(const float &pass_point, const float &location,
                                          const float &vel_linear, const int &reverse, const float &target_yaw = 0.0f);

    // IMU data processor interface
    void InitializeImuProcessor();
    void ShutdownImuProcessor();

    void PerformUnstakeModeAsync(const QRCodeLocationResult &qrcode_loc_result);
    void PerformUnstakeMode();
    void PerformUnstakeMode(const QRCodeLocationResult &qrcode_loc_result);
    void PreProcessingMowing(const QRCodeLocationResult &qrcode_loc_result);
    void ProcessQRcodeAndUnstake(const std::vector<float> &qrcode_result);
    std::vector<float> Collect_QRdata();
    std::vector<float> Process_QRdata(std::vector<float> qr_x_set, std::vector<float> qr_y_set, std::vector<float> qr_yaw_set);
    void HandleRecoveryStart();
    void HandleRecoveryEnd();

    void PerformRandomMowing();
    void PerformSpiralMowing();

    // Data validation methods
    bool CheckPerceptionFusionDataError(const PerceptionFusionResult &fusion_result);
    bool CheckMarkLocationDataError(const MarkLocationResult &mark_loc_result);
    bool CheckQRCodeLocationDataError(const QRCodeLocationResult &qrcode_loc_result);
    bool CheckImuDataError();
    bool CheckMcuExceptionDataError();
    bool CheckMotorSpeedDataError();
    void CheckTimeout(const std::string &name, const DataTimeInfo &last_time_info,
                      uint64_t timeout, DataTimeInfo &cur_time_info);
    void UpdateDataTimeInfo(const std::string &name, const DataTimeInfo &last_time_info,
                            uint64_t low_freq_time, uint32_t low_freq_count_max,
                            uint64_t cur_send_timestamp, DataTimeInfo &cur_time_info);
    void SetPerceptionFusionResult(const PerceptionFusionResult &fusion_result);
    void SetMarkLocationResultWithTimeInfo(const MarkLocationResult &mark_loc_result);
    void SetQRCodeLocationResultWithTimeInfo(const QRCodeLocationResult &qrcode_loc_result);
    void SetImuDataWithTimeInfo(const ImuData &imu_data);
    void SetMcuExceptionWithTimeInfo(const McuExceptionStatus &mcu_exception_status);
    void SetMotorSpeedDataWithTimeInfo(const MotorSpeedData &motor_speed_data);

    // Combined data update and validation function
    bool UpdateAndValidateInputData(const MarkLocationResult &mark_loc_result,
                                    const QRCodeLocationResult &qrcode_loc_result,
                                    const PerceptionFusionResult &fusion_result,
                                    McuExceptionStatus &mcu_exception_status);

    void HandleExploreCrossRegionStates(CrossRegionRunningState &cross_region_state);
    void HandleNormalCrossRegionStates(CrossRegionRunningState &cross_region_state);
    void SetSlippingStatus(bool is_slipping);

    // New
    bool IsGrassField(const GrassDetectStatus &grass_detect_status);
    bool SelfChecking();
    bool PerformSelfCheckRecovery();

    // cut border
    void ProcessCutBorderUnstakeMode();
    void ProcessCutBorderUnstakeMode(const QRCodeLocationResult &qrcode_loc_result);
    void HandleCutBorderMcuException(const ExceptionInfo &exception_info);
    void ProcessCutBorderRechargeException(RechargeRunningState recharge_state);
    void ProcessCutBorderCrossRegionException(CrossRegionRunningState cross_region_state);
    void ProcessSingleAreaCutBorderMode(const QRCodeLocationResult &qrcode_loc_result,
                                        const bool &enter_multi_region_exploration);
    void ProcessMultiAreaCutBorderMode(const MarkLocationResult &mark_loc_result,
                                       const PerceptionFusionResult &fusion_result,
                                       const bool &enter_multi_region_exploration,
                                       bool &is_beacon_valid);
    void HandleCutBorderCrossRegionStates(CrossRegionRunningState &cross_region_state);
    void ProcessingCutBorderRecharge(const QRCodeLocationResult &qrcode_loc_result);
    void TriggerExceptionPublishing();
    void PerformCutBorder(const MarkLocationResult &mark_loc_result, const QRCodeLocationResult &qrcode_loc_result,
                          const PerceptionFusionResult &fusion_result,
                          CrossRegionRunningState &cross_region_state, BehaviorRunningState &behavior_state,
                          bool is_behavior_loop, const std::vector<BehaviorExceptionType> &triggered_exception_types);

    // Test
    void Test(CrossRegionRunningState cross_region_state);

private:
    void InitSlipDetection();
    void DeinitSlipDetection();
    void SlipDetectionThread();
    void CalculateFusionPose();
    void UpdatePoseStuckDetection();
    void UpdateRollingGridMap(const PerceptionFusionResult &fusion_result, bool is_new_fusion);
    void UpdateRollingGridMapPose();
    void UpdateRollingGridMapObs(const OccupancyResult &occupancy_result);

private:
    std::chrono::steady_clock::time_point slip_start_time_;
    std::atomic_bool is_continuous_slipping_{false};

    std::mutex motor_speed_mtx_;
    MotorSpeedData motor_speed_data_;

    std::mutex motion_detection_result_mtx_;
    MotionDetectionResult motion_detection_result_;

    std::thread slip_detection_thread_;
    std::atomic_bool slip_detection_running_{false};
    std::atomic_bool is_slipping_detected_{false};
    // int slip_detection_frequency_{20}; // Detection frequency (Hz)
    float wheel_radius_ = 0.1f;     // Wheel radius (meters)
    float min_valid_linear_ = 0.1f; // Minimum valid speed (m/s)
    float min_valid_angular_ = 0.2f;
    float wheel_base_ = 0.335f;

    RandomMowerRunningState random_mower_state_{RandomMowerRunningState::NORMAL};

    // Stuck detection and recovery
    std::unique_ptr<StuckDetectionRecovery> stuck_detection_recovery_;
    std::mutex imu_data_mtx_;
    ImuData imu_data_;
    std::mutex raw_imu_data_mtx_;
    ImuData raw_imu_data_;
    std::atomic_bool is_pose_slip_{false};

private:
    std::function<void(const std::vector<FeatureSelectData> &)> feature_select_callback_;
    std::function<bool(int)> set_mark_id_callback_;
    std::function<void(CrossRegionRunningState)> cross_region_running_state_callback_;
    std::function<void(bool, bool, mower_msgs::srv::UndockOperationStatus)> undock_result_callback_;
    std::function<bool(uint64_t)> area_calc_start_callback_;
    std::function<bool(uint64_t, float &, float &)> area_calc_stop_callback_;
    std::function<void(RegionExploreResult &)> region_explore_result_callback_;
    std::function<void(bool, bool)> cut_border_result_callback_;
    std::function<void(const ob_mower_msgs::PerceptionLocalizationAlgCtrl &)> perception_localization_alg_ctrl_callback_{nullptr};
    std::function<void(int)> edge_follow_status_callback_{nullptr};

    std::unique_ptr<iox_exception_publisher> pub_exception_;
    std::unique_ptr<IceoryxPublisherMower<mower_msgs::msg::SocException>> pub_iceoryx_exception_;

    std::mutex mark_loc_mtx_;
    MarkLocationResult mark_loc_result;
    std::unique_ptr<VelocityPublisher> vel_publisher_{nullptr};

    // Running state variables
    bool is_on_docker_{false};
    MowerRunningState mower_running_state_{MowerRunningState::STOP}; // 0-Not started, 1-Running, 2-Paused
    ThreadControl thread_control_{ThreadControl::UNDEFINED};         // Control other tasks

    bool is_unstake_mode_completed_ = false; // Whether the first stage, unstake mode, is completed
    bool is_unstake_success_ = false;        // Whether unstake is successful
    bool is_power_connected_ = false;        // True means connected to power, False means not connected

    bool mcu_triggers_cross_region_ = false;       // MCU triggers cross-region thread
    bool mcu_triggers_recharge_ = false;           // MCU triggers recharge thread
    bool mcu_triggers_mower_ = false;              // MCU triggers mowing thread
    bool mcu_triggers_region_exploration_ = false; // MCU triggers exploration thread
    bool mcu_triggers_spiral_mower_ = false;       // MCU triggers spiral mowing thread
    bool mcu_triggers_cut_border_ = false;         // MCU triggers edge cutting thread

    bool mower_completed_ = false; // Whether mowing is completed
    std::deque<GrassDetectStatus> frames_;
    GrassDetectStatus grass_detect_status_;
    bool is_on_grass_field_ = false; // Whether it is on the grass

    bool is_region_explore_mode_start_{false};
    bool is_cut_border_mode_start_{false};

    /**Region exploration function processing function*/
    bool is_cooldown_active_ = false; // Used to control whether the cooldown mechanism is active
    bool is_first_enter_last_cooldown_time_ = true;
    std::chrono::steady_clock::time_point last_cooldown_time_; // Used to record the cooldown start time
    std::chrono::seconds edge_perception_drive_duration_{0};

    bool enter_multi_region_exploration_ = false; // Whether to enter multi-region exploration

    // Single region
    int qr_code_detection_count_{1}; // Number of charging pile QR code detections
    bool is_first_enter_explore_last_qr_detection_time_ = true;
    std::chrono::steady_clock::time_point last_qr_explore_detection_time_; // Last time a charging pile QR code was detected
    std::chrono::seconds qr_detection_duration_{0};
    bool is_single_area_recharge_ = false;

    // Multi region
    BeaconStatus beacon_status_;
    int current_mark_id_{-1};
    bool is_first_enter_last_mark_detection_time_ = true;

    // cross recharge params
    bool first_recharge_cross_region_ = true;
    bool cross_recharge_detection_beacon_ = true;
    std::chrono::seconds recharge_cross_region_duration_{0};
    std::chrono::steady_clock::time_point last_recharge_cross_region_time_; // Last time a charging pile QR code was detected
    int cross_recharge_mark_id_{-1};
    BeaconStatus cross_recharge_beacon_status_;
    bool recharge_edge_follow_ = true;
    float recharge_distance_ = 1.3;

    std::chrono::steady_clock::time_point last_mark_detection_time_; // Last time a charging pile QR code was detected
    std::chrono::seconds mark_detection_duration_{0};

    // New
    bool first_detection_beacon_ = true; // First detection beacon
    int next_paired_beacon_id_ = -1;     // Next pair of beacon IDs

    int region_count_{1};

    // Region exploration to get information
    mower_msgs::srv::MapResult master_region_explore_result_;
    mower_msgs::srv::MapResult slave_region_explore_result_;
    bool is_master_region_ = true;
    bool is_first_region_explore_mode_end_ = true;
    /**Region exploration function processing function*/

    /**Grass mode non-grass exception detection*/
    bool is_first_non_grass_detection_{true};
    std::chrono::steady_clock::time_point last_grass_time_;
    std::chrono::seconds non_grass_duration_{0};
    static constexpr int NON_GRASS_WARN_THRESHOLD_SECONDS{5};   // 5 second threshold
    static constexpr int NON_GRASS_ERROR_THRESHOLD_SECONDS{30}; // 30 second threshold

    std::chrono::steady_clock::time_point exception_start_time_;
    bool is_publishing_exception_{false};
    /**Grass mode non-grass exception detection*/

    /**Self-check mode*/
    bool is_self_checking_{false};                                  // Whether self-checking is in progress
    std::chrono::steady_clock::time_point self_check_start_time_;   // Self-check start time
    static constexpr int SELF_CHECK_DURATION_SECONDS{2};            // Self-check duration 2 seconds
    static constexpr int SELF_CHECK_NON_GRASS_THRESHOLD_SECONDS{1}; // 1 second threshold
    bool is_self_success_{false};
    bool is_self_recovery_active_{false};    // Whether self-check recovery is in progress
    float self_recovery_distance_{0.5};      // Self-check recovery retreat distance (m) /*param*/
    float self_recovery_linear_speed_{-0.1}; // Self-check recovery retreat speed (m/s) /*param*/
    /**Self-check mode*/

    /**cut border*/
    bool is_first_enter_cut_border_last_qr_detection_time_ = true;
    std::chrono::steady_clock::time_point last_qr_cut_border_detection_time_; // Last time a charging pile QR code was detected
    bool is_first_cut_border_mode_end_ = true;
    /**cut border*/

    /**Anti-jitter mechanism for perception-based adjustment*/
    PerceptionMovementState last_perception_movement_state_{PerceptionMovementState::IDLE}; // 上次运动状态
    std::chrono::steady_clock::time_point last_perception_movement_time_;                   // 上次运动状态改变时间
    float perception_movement_debounce_time_{1.0f};                                         // 防抖动时间间隔(秒) /*param*/
    /**Anti-jitter mechanism for perception-based adjustment*/

    /*******************************************************Algorithm parameters********************************************************************/
    // Beacon Scheduling - Unstake

    bool is_unstaking_ = false;
    bool is_enable_unstake_mode_{true}; // Whether to enable unstake mode /*param*/
    float unstake_distance_{1.0};       // Unstake distance /*param*/
    float unstake_adjust_yaw_{0.52};    // 0.52（30） Unstake adjust yaw /*param*/
    float unstake_vel_linear_{0.2};     // Unstake linear velocity /*param*/
    float unstake_vel_angular_{0.5};    // Unstake angular velocity /*param*/
    float charge_station_width_{0.4};   // Charging station width /*param*/
    float charge_station_longth_{0.5};  // Charging station length /*param*/
    float angle_proportion_{0.25};      // Rotation angle ratio /*param*/
    bool save_qr_data_{false};
    std::vector<float> qr_detect_x_;
    std::vector<float> qr_detect_y_;
    std::vector<float> qr_detect_yaw_;
    uint64_t stay_all_time_{1000};
    uint64_t stay_time_{50};
    size_t save_data_num_{3}; // Save data frame number

    float mower_linear_{0.15}; /*param*/
    float mower_angular_{0.5}; /*param*/
    // int perception_drive_cooldown_time_{5};   // Perception drive cooldown time 5s /*param*/
    int edge_mode_direction_{-1};             // Default counterclockwise -1 /*param*/
    float cross_region_adjust_yaw_{1.57};     // Adjust yaw after crossing region /*param*/
    float cross_region_adjust_displace_{0.3}; // Adjust displacement after crossing region /*param*/
    float mark_distance_threshold_{0.5};      // 0.5 Distance threshold between beacon and mower camera to determine if in region /*param*/
    float camera_2_center_dis_{0.37};         // Distance from mower camera to rotation center: Chelsea(0.37) Greebo(0.45) /*param*/

    // New parameters
    int edge_perception_drive_cooldown_time_threshold_{10}; // 10s Edge perception drive cooldown time /*param*/
    int qr_detection_cooldown_time_threshold_{30};          // 60s Edge perception drive cooldown time /*param*/
    int mark_detection_cooldown_time_threshold_{30};        // 60s Edge perception drive cooldown time /*param*/

    // Region exploration
    float recharge_distance_threshold_{1.2}; // Region exploration recharge distance threshold

    // Pre-mowing processing
    float mower_start_qr_distance_threshold_{0.8}; // Distance threshold between mower and QR code before mowing/*param*/

    // Stuck detection data logging control
    bool enable_stuck_detection_data_logging_{false}; // Whether to enable stuck detection data logging /*param*/

private:
    // Slip detection
    std::chrono::steady_clock::time_point last_zero_velocity_time_; // Record the start time when the speed is close to 0
    bool is_velocity_zero_ = false;                                 // Mark whether the current speed is close to 0
    float linear_velocity_threshold_ = 0.02f;                       // Speed threshold (m/s or rad/s) /*param*/
    float angular_velocity_threshold_ = 0.02f;                      // Speed threshold (m/s or rad/s) /*param*/
    static constexpr int ZERO_VELOCITY_THRESHOLD_SECONDS{1};        // Duration threshold (1 second)

    // Add member variables
    std::atomic_bool is_recovery_active_{false}; // Mark whether a recovery operation is in progress
    std::mutex recovery_mutex_;
    std::condition_variable recovery_cv_;
    std::future<void> unstake_future_; // Future of asynchronous task

private:
    uint64_t last_imu_timestamp_{0};         // Last received IMU data timestamp
    uint64_t motion_detection_timestamp_{0}; // Last received motion detection data timestamp

    /*******************************************************Algorithm parameters********************************************************************/

private:
    float test_linear_speed_ = 0.0f;                        // Adjustable linear speed parameter/*param*/
    float test_angular_speed_ = 0.5f;                       // Adjustable angular speed parameter/*param*/
    int64_t test_duration_ms_ = 6280;                       // Test duration/*param*/
    bool is_test_running_ = false;                          // Test status flag
    std::chrono::steady_clock::time_point test_start_time_; // Test start time

    std::shared_ptr<PoseFusion> pose_fusion_{nullptr};
    ob_mower_msgs::NavFusionPose fusion_pose_;

    std::mutex slope_detection_result_mtx_;
    SlopeDetectionResult slope_detection_result_;

    bool is_recover_from_exception_{false};
    bool is_behavior_loop_{false};
    // 上一次触发的异常类型，用来通知各个功能模块，比如之前触发过打滑
    std::vector<BehaviorExceptionType> last_triggered_exception_types_;

    std::unique_ptr<PoseStuckDetector> pose_stuck_detector_{nullptr};
    std::atomic_bool is_stuck_detected_{false};

    AccelerationFilterData acceleration_filter_data_;
    FusionData fusion_data_;
    IMUCalibrationConfig imu_calibration_config_;
    bool is_imu_calibration_valid_ = false;
    std::mutex function_state_mtx_;
    fescue_msgs_enum_FunctionState function_state_{fescue_msgs_enum_FunctionState::FUNCTION_STATE_INVALID};
    uint64_t function_state_timestamp_{0};

    // Data time information management for input data validation
    std::mutex data_time_info_map_mtx_;
    std::unordered_map<std::string, DataTimeInfo> data_time_info_map_;

    std::shared_ptr<RollingGridMap> rolling_grid_map_{nullptr};
    std::vector<Point2f> front_bev_blind_spot_;

    std::mutex dangerous_point_cloud_mtx_;
    std::vector<Point2f> dangerous_point_cloud_;
    Pose2f dangerous_point_cloud_pose_;
    uint64_t last_update_point_cloud_time_{0};
    bool is_new_dangerous_point_cloud_{false};

private:
    // IMU data processor
    std::unique_ptr<ImuDataProcessor> imu_processor_;
    ImuProcessorParam imu_processor_param_;
};

} // namespace fescue_iox

#endif
