#include "edge_follow_node.hpp"

#include "edge_follow_node_config.hpp"
#include "mower_msgs/msg/slope_detection_result.hpp"
#include "mower_sdk_version.h"
#include "process_fusion.hpp"
#include "utils/dir.hpp"
#include "utils/logger.hpp"
#include "utils/rate.hpp"
#include "utils/time.hpp"
#include "utils/utils.hpp"
#include "yaml-cpp/yaml.h"

#include <algorithm>
#include <chrono>
#include <cmath>      // for std::labs()
#include <filesystem> //c++17
#include <limits>
#include <memory>
#include <string>
#include <utility>
#include <vector>

namespace fescue_iox
{

NavigationEdgeFollowNode::NavigationEdgeFollowNode(const std::string &node_name)
    : node_name_(node_name)
{
    InitWorkingDirectory();
    InitParam();
    InitLogger();
    InitSubscriber();
    InitPublisher();
    InitService();
    InitAlgorithm();
    InitHeartbeat();
}

NavigationEdgeFollowNode::~NavigationEdgeFollowNode()
{
    DeinitAlgorithm();
    LOG_WARN("NavigationEdgeFollowNode exit!");
}

void NavigationEdgeFollowNode::InitWorkingDirectory()
{
    std::string working_directory = SetWorkingDirectory("/../");
    LOG_INFO("{} working directory is: {}", node_name_.c_str(), working_directory.c_str());
}

void NavigationEdgeFollowNode::InitParam()
{
    const std::string conf_file{"conf/navigation_edge_follow_node/navigation_edge_follow_node.yaml"};
    std::string conf_path = GetDirectoryPath(conf_file);
    if (!conf_path.empty())
    {
        LOG_INFO("NavigationEdgeFollowNode create config path: {}", conf_path.c_str());
        if (!CreateDirectories(conf_path))
        {
            LOG_ERROR("NavigationEdgeFollowNode create config path failed!!!");
        }
    }
    if (!Config<NavigationEdgeFollowNodeConfig>::Init(conf_file))
    {
        LOG_WARN("Init NavigationEdgeFollowNode config parameters failed!");
    }
    NavigationEdgeFollowNodeConfig config = Config<NavigationEdgeFollowNodeConfig>::GetConfig();

    LOG_INFO("[navigation_edge_follow_node] git tag: {}", _GIT_TAG_);
    LOG_INFO("[navigation_edge_follow_node] git version: {}", _GIT_VERSION_);
    LOG_INFO("[navigation_edge_follow_node] compile time: {}", _COMPILE_TIME_);

    LOG_INFO("{}", config.toString().c_str());

    log_dir_ = config.common_conf.log_dir;
    console_log_level_ = config.common_conf.console_log_level;
    file_log_level_ = config.common_conf.file_log_level;
    edge_follow_alg_conf_file_ = config.edge_follow_alg_conf_file;

    if (!Config<NavigationEdgeFollowNodeConfig>::SetConfig(config, true))
    {
        LOG_WARN("Set NavigationEdgeFollowNodeConfig config parameters failed!");
    }

    CreateDirectories(log_dir_);
}

void NavigationEdgeFollowNode::InitAlgorithmParam()
{
    std::string conf_path = GetDirectoryPath(edge_follow_alg_conf_file_);
    if (!conf_path.empty())
    {
        LOG_INFO("Navigation edge follow algo create config path: {}", conf_path.c_str());
        if (!CreateDirectories(conf_path))
        {
            LOG_ERROR("Navigation edge follow algo create config path failed!!!");
        }
    }
    if (!Config<NavigationEdgeFollowAlgConfig>::Init(edge_follow_alg_conf_file_))
    {
        LOG_WARN("Init Navigation edge follow algo config parameters failed!");
    }
    NavigationEdgeFollowAlgConfig config = Config<NavigationEdgeFollowAlgConfig>::GetConfig();
    LOG_INFO("{}", config.toString().c_str());
    ConfigParamToAlgorithmParam(config, edge_follow_alg_param_);
    if (!Config<NavigationEdgeFollowAlgConfig>::SetConfig(config, true))
    {
        LOG_WARN("Set Navigation edge follow algo config parameters failed!");
    }
}

void NavigationEdgeFollowNode::ConfigParamToAlgorithmParam(const NavigationEdgeFollowAlgConfig &config, EdgeFollowAlgParam &param)
{
    param.edge_follow_alg_type = config.edge_follow_alg_type;
    param.flAction_type = config.flAction_type;
    param.vel_smooth_enable = config.vel_smooth_enable;

    param.edge_follow_linear = config.edge_follow_linear;
    param.edge_follow_angular = config.edge_follow_angular;

    param.dividing_line_left = config.dividing_line_left;
    param.dividing_line_right = config.dividing_line_right;

    param.max_obstacle_avoid_duration_times = config.max_obstacle_avoid_duration_times; // 避障走直线持续发送速度次数
    param.max_turn_right_times = config.max_turn_right_times;                           // 右转次数最大次数
    param.min_vel_angular = config.min_vel_angular;                                     // 最小有效角速度
    param.max_zero_vel_times = config.max_zero_vel_times;

    // 义感兴趣区域(ROI)，以矩形的左上角点（x, y）和宽度、高度
    param.x = config.x;           // 左上角点的x坐标
    param.y = config.y;           // 左上角点的y坐标
    param.width = config.width;   // 矩形区域的宽度
    param.height = config.height; // 矩形区域的高度

    // occipancy grid
    param.acc = config.acc;
    param.slow_acc = config.slow_acc;
    param.kp_r = config.kp_r;
    param.kp_l = config.kp_l;
    param.wheel_base = config.wheel_base;
    param.left_line_x = config.left_line_x;
    param.right_line_x = config.right_line_x;
    param.resolution = config.resolution;
    param.look_ahead_dis = config.look_ahead_dis;
    param.danger_dis = config.danger_dis;
    param.grid_width = config.grid_width;
    param.grid_height = config.grid_height;
    param.v_max = config.v_max;
    param.max_fl_time = config.max_fl_time;
    param.fl_back_right_target_dis = config.fl_back_right_target_dis;
    param.fl_turn_right_target = config.fl_turn_right_target;

    param.fl_forward_target_dis = config.fl_forward_target_dis;
    param.fl_forward_r_target_dis = config.fl_forward_r_target_dis;
    param.fl_go_straight_linear = config.fl_go_straight_linear;
    param.fl_go_straight_angluar = config.fl_go_straight_angluar;
    param.fl_turn_r_linear = config.fl_turn_r_linear;
    param.fl_turn_r_angular = config.fl_turn_r_angular;

    param.fl_spot_turn_r_angular = config.fl_spot_turn_r_angular;
    param.fl_turn_right_target = config.fl_turn_right_target;
    param.fl_turn_right_target_new = config.fl_turn_right_target_new;
    param.fl_turn_r_angular_new = config.fl_turn_r_angular_new;
    param.fl_tuning_linear = config.fl_tuning_linear;
    param.fl_tuning_angular = config.fl_tuning_angular;

    param.max_ao_turn_l_spot_num = config.max_ao_turn_l_spot_num;
    param.max_ao_turn_l_num = config.max_ao_turn_l_num;
    param.ao_turn_l_spot_angular = config.ao_turn_l_spot_angular;
    param.ao_turn_l_wheel_r = config.ao_turn_l_wheel_r;
    param.ao_turn_l_wheel_l = config.ao_turn_l_wheel_l;
    param.max_ao_time = config.max_ao_time;

    // 算法配置文件
    param.predict_trajectory_conf = config.predict_trajectory_conf;
    param.velocity_smooth_conf = config.velocity_smooth_conf;
    param.path_track_conf = config.path_track_conf;
}

void NavigationEdgeFollowNode::InitAlgorithm()
{
    InitAlgorithmParam();
    edge_follow_alg_param_.dead_zone = GetBEVBlindZoneDist();
    edge_follow_alg_ = std::make_unique<NavigationEdgeFollowAlg>(edge_follow_alg_param_);
    thread_running_.store(true);
    edge_follow_thread_ = std::thread(&NavigationEdgeFollowNode::EdgeFollowThread, this);
}

void NavigationEdgeFollowNode::DeinitAlgorithm()
{
    if (edge_follow_alg_)
    {
        edge_follow_alg_->SetVelPublisherProhibit(true);
    }
    thread_running_.store(false);
    edge_follow_thread_.join();
}

void NavigationEdgeFollowNode::InitLogger()
{
    std::string log_file_name = log_dir_ + "/" + node_name_ + ".log";
    SpdlogParams params(node_name_, console_log_level_, file_log_level_, log_file_name);
    InitSpdlogParams(params);
}

void NavigationEdgeFollowNode::InitHeartbeat()
{
    pub_heartbeat_ = std::make_unique<NodeHeartbeatPublisher>();
    pub_heartbeat_->start();
}

void NavigationEdgeFollowNode::InitSubscriber()
{
    sub_fusion_result_ = std::make_unique<IceoryxSubscriberMower<fescue_msgs__msg__PerceptionFusionResult>>(
        "fusion_result", 1, [this](const fescue_msgs__msg__PerceptionFusionResult &data, const std::string &event) {
            (void)event;
            DealPerceptionFusionResult(data);
        });
    sub_odom_twist_ = std::make_unique<IceoryxSubscriberMower<geometry_msgs__msg__Twist_iox>>(
        "chassis_odom_result", 1, [this](const geometry_msgs__msg__Twist_iox &data, const std::string &event) {
            (void)event;
            DealOdomTwistResult(data);
        });
    sub_soc_imu_ = std::make_unique<IceoryxSubscriberMower<mower_msgs::msg::SocImu>>(
        "soc_imu", 1, [this](const mower_msgs::msg::SocImu &data, const std::string &event) {
            (void)event;
            DealSocImu(data);
        });
    sub_nav_alg_ctrl_ = std::make_unique<IceoryxSubscriberMower<fescue_msgs__msg__NavigationAlgoCtrlData>>(
        "navigation_nav_alg_ctrl", 10, [this](const fescue_msgs__msg__NavigationAlgoCtrlData &data, const std::string &event) {
            (void)event;
            DealNavAlgCtrlResult(data);
        });
    sub_nav_running_state_ = std::make_unique<IceoryxSubscriberMower<fescue_msgs__msg__NavigationRunningStateData>>(
        "navigation_running_state", 10, [this](const fescue_msgs__msg__NavigationRunningStateData &data, const std::string &event) {
            (void)event;
            DealNavRunningState(data);
        });

    sub_escape_result_ = std::make_unique<IceoryxSubscriberMower<ob_mower_msgs::NavEscapeResult>>(
        "navigation_escape_result", 10, [this](const ob_mower_msgs::NavEscapeResult &data, const std::string &event) {
            (void)event;
            DealEscapeResult(data);
        });
    sub_loc_slope_detection_result_ = std::make_unique<IceoryxSubscriberMower<mower_msgs::msg::LawnmowerSlopeStatus>>(
        "localization_slope_detection_result", 1, [this](const mower_msgs::msg::LawnmowerSlopeStatus &data, const std::string &event) {
            (void)event;
            DealSlopeDetectionResult(data);
        });
}

void NavigationEdgeFollowNode::InitPublisher()
{
    pub_function_state_ = std::make_unique<IceoryxPublisherMower<fescue_msgs_FunctionStateData>>("navigation_function_state");
}

void NavigationEdgeFollowNode::DealSlopeDetectionResult(const mower_msgs::msg::LawnmowerSlopeStatus &data)
{
    std::lock_guard<std::mutex> lock(slope_detection_result_mtx_);
    slope_detection_result_.yaw = data.yaw;
    slope_detection_result_.pitch = data.pitch;
    slope_detection_result_.roll = data.roll;
    slope_detection_result_.quaternion_x = data.quaternion_x;
    slope_detection_result_.quaternion_y = data.quaternion_y;
    slope_detection_result_.quaternion_z = data.quaternion_z;
    slope_detection_result_.quaternion_w = data.quaternion_w;
    slope_detection_result_.timestamp_ms = data.timestamp_ms;
    slope_detection_result_.slope_status = data.slope_status;
}
void NavigationEdgeFollowNode::DealEscapeResult(const ob_mower_msgs::NavEscapeResult &data)
{
    std::lock_guard<std::mutex> lock(escape_result_mutex_);
    escape_result_.timestamp_ms = data.timestamp_ms;
    escape_result_.is_loop = data.is_loop;
    // LOG_ERROR("#### receive escape result: {} ,{}", data.is_loop, data.timestamp_ms);
}

void NavigationEdgeFollowNode::DealNavRunningState(const fescue_msgs__msg__NavigationRunningStateData &data)
{
    MowerRunningState state = static_cast<MowerRunningState>(data.state);
    if (state == MowerRunningState::PAUSE)
    {
        ResetStatus();
    }
    if (edge_follow_alg_)
    {
        edge_follow_alg_->SetAlgoRunningState(state);
    }
}

void NavigationEdgeFollowNode::ResetStatus()
{
    is_recover_from_exception_.store(false);
}

void NavigationEdgeFollowNode::DealPerceptionFusionResult(const fescue_msgs__msg__PerceptionFusionResult &msg)
{
    std::lock_guard<std::mutex> lock(fusion_result_mutex_);
    GetFusionGrassDetectStatus(msg, fusion_result_.grass_detecte_status);
    GetFusionObstacleResult(msg, fusion_result_.obstacle_result);
    GetFusionBoundaryResult(msg, fusion_result_.boundary_result);
    GetFusionOccupancyResult(msg, fusion_result_.occupancy_grid);
    fusion_result_.mower_point.x = msg.mower_point.x;
    fusion_result_.mower_point.y = msg.mower_point.y;
    fusion_result_.min_dist_point.x = msg.min_dist_point.x;
    fusion_result_.min_dist_point.y = msg.min_dist_point.y;
    fusion_result_.input_timestamp = msg.timestamp;
    fusion_result_.output_timestamp = msg.output_timestamp;
}

void NavigationEdgeFollowNode::DealOdomTwistResult(const geometry_msgs__msg__Twist_iox &msg)
{
    std::lock_guard<std::mutex> lock(odom_result_mutex_);
    odom_result_.linear = msg.linear.x;
    odom_result_.angular = msg.angular.z;
}
// soc imu_x = body_x,imu_y = -body_y,imu_z = -body_z
void NavigationEdgeFollowNode::DealSocImu(const mower_msgs::msg::SocImu &data)
{
    std::lock_guard<std::mutex> lock(imu_mtx_);
    imu_data_.angular_velocity_x = data.angular_velocity_x;
    imu_data_.angular_velocity_y = data.angular_velocity_y;
    imu_data_.angular_velocity_z = data.angular_velocity_z;
    imu_data_.linear_acceleration_x = data.linear_acceleration_x;
    imu_data_.linear_acceleration_y = data.linear_acceleration_y;
    imu_data_.linear_acceleration_z = data.linear_acceleration_z;
    imu_data_.frame_timestamp = data.frame_timestamp;
    imu_data_.system_timestamp = data.system_timestamp;
    LOG_DEBUG_THROTTLE(1000, "acc_x:{},acc_y:{},acc_z:{}", data.linear_acceleration_x, data.linear_acceleration_y, data.linear_acceleration_z);
    LOG_DEBUG_THROTTLE(1000, "w_x:{},w_y:{},w_z:{}", data.angular_velocity_x, data.angular_velocity_y, data.angular_velocity_z);
    // update IMU datas

    if (!bias_complete_)
    {
        if (imu_datas_.size() < 150)
        {
            imu_datas_.push_back(imu_data_);
        }
        else
        {
            Eigen::Vector3d bias = Eigen::Vector3d::Zero();
            for (size_t i = 0; i < imu_datas_.size(); i++)
            {
                bias[0] += imu_datas_[i].angular_velocity_x;
                bias[1] += imu_datas_[i].angular_velocity_y;
                bias[2] += imu_datas_[i].angular_velocity_z;
            }
            bias /= imu_datas_.size();
            bias_ = bias;
            bias_complete_ = true;
            LOG_ERROR("*********edge_follow bias complete:{},bias:{}*************", bias_complete_, bias_[2]);
        }
    }
}

void NavigationEdgeFollowNode::DealNavAlgCtrlResult(const fescue_msgs__msg__NavigationAlgoCtrlData &msg)
{
    for (size_t i = 0; i < msg.data.size(); i++)
    {
        if (msg.data[i].type == FESCUE_MSGS_ENUM_NAV_ALGO_TYPE_EDGE_FOLLOW)
        {
            if (msg.data[i].is_behavior_loop && !edge_follow_enable_.load() && msg.data[i].state == FESCUE_MSGS_ENUM_NAV_ALGO_STATE_ENABLE)
            {
                LOG_INFO("recover from exception");
                is_recover_from_exception_.store(true);
            }
            // LOG_INFO("last_triggered_exception_types size: {}", msg.data[i].last_triggered_exception_types.size());
            // for (size_t j = 0; j < msg.data[i].last_triggered_exception_types.size(); j++)
            // {
            //     LOG_INFO("last_triggered_exception_types: {}", static_cast<int>(msg.data[i].last_triggered_exception_types[j]));
            // }
            switch (msg.data[i].state)
            {
            case FESCUE_MSGS_ENUM_NAV_ALGO_STATE_DISABLE:
                edge_follow_enable_.store(false);
                break;
            case FESCUE_MSGS_ENUM_NAV_ALGO_STATE_ENABLE:
                edge_follow_enable_.store(true);
                break;
            default:
                break;
            }
            break;
        }
    }
}

void NavigationEdgeFollowNode::EdgeFollowThread()
{
    PerceptionFusionResult fusion_result;
    OdomResult odom_result;
    ob_mower_msgs::NavEscapeResult escape_result;
    ImuData imu_data;
    mower_msgs::msg::LawnmowerSlopeStatus slope_detection_result;
    while (thread_running_.load())
    {
        if (edge_follow_enable_.load())
        {
            {
                std::scoped_lock lock(fusion_result_mutex_, odom_result_mutex_, escape_result_mutex_, imu_mtx_, slope_detection_result_mtx_);
                fusion_result = fusion_result_;
                odom_result = odom_result_;
                escape_result = escape_result_;
                imu_data = imu_data_;
                slope_detection_result = slope_detection_result_;
            }
            if (edge_follow_alg_)
            {
                if (bias_complete_)
                {
                    odom_result.angular = -imu_data.angular_velocity_z + bias_[2];
                }
                else
                {
                    odom_result.angular = -imu_data.angular_velocity_z;
                }
                if (is_recover_from_exception_.load())
                {
                    LOG_INFO("edge follow prepare to recover from exception");
                    edge_follow_alg_->PrepareRecoverFromException();
                    is_recover_from_exception_.store(false);
                }

                edge_follow_alg_->DoEdgeFollow(fusion_result, odom_result, escape_result, slope_detection_result);
                auto mower_running_state = edge_follow_alg_->GetMowerRunningState();
                if (pub_function_state_ && mower_running_state == MowerRunningState::RUNNING)
                {
                    fescue_msgs_FunctionStateData function_state_data;
                    function_state_data.state = fescue_msgs_enum_FunctionState::FUNCTION_STATE_EDGE_FOLLOW;
                    pub_function_state_->publish(function_state_data);
                }
            }
        }
        else
        {
            LOG_DEBUG_THROTTLE(2000, "NavigationEdgeFollowAlg disable!");
            if (edge_follow_alg_)
            {
                edge_follow_alg_->ResetEdgeFollowFlags();
            }
        }
        std::this_thread::sleep_for(std::chrono::milliseconds(5));
    }
}

void NavigationEdgeFollowNode::InitService()
{
    service_get_node_param_ = std::make_unique<IceoryxServerMower<get_node_param_request, get_node_param_response>>(
        "get_navigation_edge_follow_node_param_request", 10U,
        [this](const get_node_param_request &request, get_node_param_response &response) {
            (void)request;
            response.success = GetEdgeFollowNodeParam(response.data);
            LOG_INFO("Get navigation edge follow node param execute {}", response.success);
        });

    service_set_node_param_ = std::make_unique<IceoryxServerMower<set_node_param_request, set_node_param_response>>(
        "set_navigation_edge_follow_node_param_request", 10U,
        [this](const set_node_param_request &request, set_node_param_response &response) {
            response.success = SetEdgeFollowNodeParam(request.data);
            LOG_INFO("Set navigation edge follow node param execute {}", response.success);
        });

    service_get_alg_param_ = std::make_unique<IceoryxServerMower<get_alg_param_request, get_alg_param_response>>(
        "get_navigation_edge_follow_alg_param_request", 10U,
        [this](const get_alg_param_request &request, get_alg_param_response &response) {
            (void)request;
            response.success = DealGetAlgParam(response.data);
            LOG_INFO("Get navigation edge follow alg param execute {}", response.success);
        });

    service_set_alg_param_ = std::make_unique<IceoryxServerMower<set_alg_param_request, set_alg_param_response>>(
        "set_navigation_edge_follow_alg_param_request", 10U,
        [this](const set_alg_param_request &request, set_alg_param_response &response) {
            response.success = DealSetAlgParam(request.data);
            LOG_INFO("Set navigation edge follow alg param execute {}", response.success);
        });
}

bool NavigationEdgeFollowNode::GetEdgeFollowNodeParam(ob_mower_srvs::NodeParamData &data)
{
    data.console_log_level.unsafe_assign(console_log_level_.c_str());
    data.file_log_level.unsafe_assign(file_log_level_.c_str());
    return true;
}

bool NavigationEdgeFollowNode::SetEdgeFollowNodeParam(const ob_mower_srvs::NodeParamData &data)
{
    console_log_level_ = std::string(data.console_log_level.c_str());
    file_log_level_ = std::string(data.file_log_level.c_str());
    InitLogger();
    NavigationEdgeFollowNodeConfig config = Config<NavigationEdgeFollowNodeConfig>::GetConfig();
    config.common_conf.console_log_level = console_log_level_;
    config.common_conf.file_log_level = file_log_level_;
    if (!Config<NavigationEdgeFollowNodeConfig>::SetConfig(config))
    {
        LOG_WARN("Set NavigationEdgeFollowNode config parameters failed!");
        return false;
    }
    LOG_INFO("New NavigationEdgeFollowNode params: {}", config.toString().c_str());
    return true;
}

bool NavigationEdgeFollowNode::DealGetAlgParam(ob_mower_srvs::EdgeFollowAlgParam &data)
{
    if (!edge_follow_alg_)
        return false;

    EdgeFollowAlgParam param;
    edge_follow_alg_->GetAlgParam(param);
    data.acc = param.acc;
    data.slow_acc = param.slow_acc;
    data.max_linear = param.v_max;
    data.kp_r = param.kp_r;
    data.kp_l = param.kp_l;
    return true;
}

bool NavigationEdgeFollowNode::DealSetAlgParam(const ob_mower_srvs::EdgeFollowAlgParam &data)
{
    if (!edge_follow_alg_)
        return false;

    EdgeFollowAlgParam param;
    edge_follow_alg_->GetAlgParam(param);
    param.acc = data.acc;
    param.slow_acc = data.slow_acc;
    param.v_max = data.max_linear;
    param.kp_r = data.kp_r;
    param.kp_l = data.kp_l;
    NavigationEdgeFollowAlgConfig config = Config<NavigationEdgeFollowAlgConfig>::GetConfig();
    config.acc = param.acc;
    config.slow_acc = param.slow_acc;
    config.v_max = param.v_max;
    config.kp_r = param.kp_r;
    config.kp_l = param.kp_l;
    Config<NavigationEdgeFollowAlgConfig>::SetConfig(config);
    return edge_follow_alg_->SetAlgParam(param);
}

float NavigationEdgeFollowNode::GetBEVBlindZoneDist()
{
    float bev_zone_dist = 0.15; // defaut value is 0.15
    auto client = std::make_unique<IceoryxClientMower<mower_msgs::srv::CameraBevParamsRequest,
                                                      mower_msgs::srv::CameraBevParamsResponse>>("get_union_rgb_camera_bev_params");

    auto response_handler = [](const mower_msgs::srv::CameraBevParamsResponse &response_receive,
                               mower_msgs::srv::CameraBevParamsResponse &response_output) -> bool {
        response_output.bev_params.scotoma_distance_ = response_receive.bev_params.scotoma_distance_;
        response_output.success = response_receive.success;
        return response_output.success;
    };

    mower_msgs::srv::CameraBevParamsRequest request_input;
    mower_msgs::srv::CameraBevParamsResponse response_output;
    if (client->SendRequest(request_input, response_output, nullptr, response_handler))
    {
        bev_zone_dist = response_output.bev_params.scotoma_distance_;
        LOG_INFO("Edge follow alg get bev zone dist success, use {:.2f}!", bev_zone_dist);
    }
    else
    {
        LOG_WARN("Edge follow alg get bev zone dist fail!, use default {:.2f}", bev_zone_dist);
    }
    return bev_zone_dist;
}

} // namespace fescue_iox
