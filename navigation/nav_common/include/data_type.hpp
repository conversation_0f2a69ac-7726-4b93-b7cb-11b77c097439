#ifndef NAV_DATA_TYPE_HPP
#define NAV_DATA_TYPE_HPP

#include "opencv2/opencv.hpp"
#include "utils/math_type.hpp"

#include <cstdint>
#include <vector>

namespace fescue_iox
{

enum class McuExceptionStatus : int
{
    NORMAL = 0,    // Normal situation
    COLLISION = 1, // Collision exception
    LIFTING = 2,   // Lifting exception
    UNKNOWN = -1   // Unknown status
};

// Define direction enumeration
enum class Direction : int
{
    UNDEFINED = -1, // Undefined
    LEFT_TURN = 0,  // Left turn
    RIGHT_TURN = 1  // Right turn
};

// Define the entry and exit status of the beacon
enum class Beaconstatus : int
{
    ENTRY = 0,    // Enter channel
    EXIT = 1,     // Exit channel
    UNDEFINED = 2 // Undefined
};

enum class ThreadControl : int
{
    PERCEPTION_EDGE_THREAD = 0, // Perception edge thread
    CROSS_REGION_THREAD = 1,    // Cross-region thread
    RECHARGE_THREAD = 2,        // Recharge thread
    RANDOM_MOWING_THREAD = 3,   // Random mowing thread
    BEHAVIOR_THREAD = 4,        // Recovery thread
    SPIRAL_MOWING_THREAD = 5,   // Spiral mowing thread
    // CUT_BORDER_THREAD = 6,      // Border cutting thread
    ESCAPE_THREAD = 6, // Escape thread
    CLOSE_ALL_TASK,    // Close all tasks
    UNDEFINED          // Undefined
};

// Recovery status
enum class BehaviorRunningState : int
{

    SUCCESS = 0, // Recovery node executed successfully
    FAILURE,     // Recovery node execution failed
    RUNNING,     // Recovery node execution in progress
    UNDEFINED    // Undefined
};

// Cross-region status
enum class CrossRegionRunningState : int
{
    EDGE_FINDING_BEACON = 0,                 // Added edge finding beacon status /**This state allows switching states */ /**Recovery mode can be adopted */
    PER_FOUND_BEACON,                        // Perception has found beacon /**This state allows switching states */ /**Recovery mode can be adopted */
    STAGE3_LOC_DETECT_BEACON_WITH_POS,       // Stage 3, positioning detects QR code, can calculate pose /**State switching is not allowed */ /**Recovery mode cannot be adopted */
    STAGE4_ENTERING_CHANNEL,                 // Stage 4, just entering the channel and has traveled a certain distance in a straight line /**State switching is not allowed */ /**Recovery mode cannot be adopted */
    STAGE4_LOC_NO_DETECT_BEACON,             // Stage 4, positioning does not detect QR code /**State switching is not allowed */ /**Recovery mode cannot be adopted */
    STAGE4_NON_GRASS_REACHED,                // Stage 4, non-grass area reached /**State switching is not allowed */ /**Recovery mode cannot be adopted */
    STAGE4_LOC_DETECT_BEACON_NO_POS,         // Stage 4, positioning detects QR code, cannot calculate pose /**State switching is not allowed */ /**Recovery mode cannot be adopted */
    STAGE4_LOC_DETECT_BEACON_WITH_POS,       // Stage 4, positioning detects QR code, can calculate pose /**State switching is not allowed */ /**Recovery mode cannot be adopted */
    STAGE4_BEACON_EXIT_CROSS_REGION,         // Stage 4, beacon detection exits cross-region /**State switching is not allowed */ /**Recovery mode cannot be adopted */
    STAGE4_NONGRASS2GRASS_EXIT_CROSS_REGION, // Stage 4, non-grass to grass exits cross-region /**State switching is not allowed */ /**Recovery mode cannot be adopted */

    START,
    FINISH,
    UNDEFINED // Undefined /**This state allows switching states */ /**Recovery mode can be adopted */
};

inline constexpr const char *asStringLiteral(const CrossRegionRunningState state) noexcept
{
    switch (state)
    {
    case CrossRegionRunningState::EDGE_FINDING_BEACON:
        return "CrossRegionRunningState::EDGE_FINDING_BEACON";
    case CrossRegionRunningState::PER_FOUND_BEACON:
        return "CrossRegionRunningState::PER_FOUND_BEACON";
    case CrossRegionRunningState::STAGE3_LOC_DETECT_BEACON_WITH_POS:
        return "CrossRegionRunningState::STAGE3_LOC_DETECT_BEACON_WITH_POS";
    case CrossRegionRunningState::STAGE4_ENTERING_CHANNEL:
        return "CrossRegionRunningState::STAGE4_ENTERING_CHANNEL";
    case CrossRegionRunningState::STAGE4_LOC_NO_DETECT_BEACON:
        return "CrossRegionRunningState::STAGE4_LOC_NO_DETECT_BEACON";
    case CrossRegionRunningState::STAGE4_NON_GRASS_REACHED:
        return "CrossRegionRunningState::STAGE4_NON_GRASS_REACHED";
    case CrossRegionRunningState::STAGE4_LOC_DETECT_BEACON_NO_POS:
        return "CrossRegionRunningState::STAGE4_LOC_DETECT_BEACON_NO_POS";
    case CrossRegionRunningState::STAGE4_LOC_DETECT_BEACON_WITH_POS:
        return "CrossRegionRunningState::STAGE4_LOC_DETECT_BEACON_WITH_POS";
    case CrossRegionRunningState::STAGE4_BEACON_EXIT_CROSS_REGION:
        return "CrossRegionRunningState::STAGE4_BEACON_EXIT_CROSS_REGION";
    case CrossRegionRunningState::STAGE4_NONGRASS2GRASS_EXIT_CROSS_REGION:
        return "CrossRegionRunningState::STAGE4_NONGRASS2GRASS_EXIT_CROSS_REGION";
    case CrossRegionRunningState::START:
        return "CrossRegionRunningState::START";
    case CrossRegionRunningState::FINISH:
        return "CrossRegionRunningState::FINISH";
    case CrossRegionRunningState::UNDEFINED:
        return "CrossRegionRunningState::UNDEFINED";
    }
    return "CrossRegionRunningState::UNDEFINED";
}

// Recharge status
enum class RechargeRunningState : int
{
    BACK_GROUND_RUN = 0, // 后台运行，执行沿边
    ADJUST_TO_STATION,   // 已采集了二维码定位数据，调整小车到桩正前方
    ACCURATE_DOCK,       // 调整到桩正前方后进行精准对桩（不可打断状态）
    RECHARGE_FINISH,     // 回充完成
    UNDEFINED            // 初始化默认状态
};

inline constexpr const char *asStringLiteral(const RechargeRunningState state) noexcept
{
    switch (state)
    {
    case RechargeRunningState::BACK_GROUND_RUN:
        return "RechargeRunningState::BACK_GROUND_RUN";
    case RechargeRunningState::ADJUST_TO_STATION:
        return "RechargeRunningState::ADJUST_TO_STATION";
    case RechargeRunningState::ACCURATE_DOCK:
        return "RechargeRunningState::ACCURATE_DOCK";
    case RechargeRunningState::RECHARGE_FINISH:
        return "RechargeRunningState::RECHARGE_FINISH";
    case RechargeRunningState::UNDEFINED:
        return "RechargeRunningState::UNDEFINED";
    }
    return "RechargeRunningState::UNDEFINED";
}

enum class NavAlgCtrlState : int
{
    IGNORE = -1,
    DISABLE = 0,
    ENABLE = 1
};

enum class RechargeState : int
{
    IGNORE = -1,
    DISABLE = 0,
    ENABLE = 1
};

enum class CrossRegionState : int
{
    IGNORE = -1,
    DISABLE = 0,
    ENABLE = 1
};

enum class EdgeFollowState : int
{
    IGNORE = -1,
    DISABLE = 0,
    ENABLE = 1
};

enum class RandomMowerState : int
{
    IGNORE = -1,
    DISABLE = 0,
    ENABLE = 1
};

enum class MowerState : int
{
    IGNORE = -1,
    DISABLE = 0,
    ENABLE = 1
};

enum class RandomMowerRunningState : int
{
    // STOP = 0,
    // RUNNING = 1,
    // FINISH = 2
    NORMAL = 0,
    TRAP_WAIT_BIAS = 1,
    TRAP_EDGE_FOLLOW = 2,
    UNDEFINED,
};

inline constexpr const char *asStringLiteral(const RandomMowerRunningState state) noexcept
{
    switch (state)
    {
    case RandomMowerRunningState::NORMAL:
        return "RandomMowerRunningState::NORMAL";
    case RandomMowerRunningState::TRAP_WAIT_BIAS:
        return "RandomMowerRunningState::TRAP_WAIT_BIAS";
    case RandomMowerRunningState::TRAP_EDGE_FOLLOW:
        return "RandomMowerRunningState::TRAP_EDGE_FOLLOW";
    case RandomMowerRunningState::UNDEFINED:
        return "RandomMowerRunningState::UNDEFINED";
    default:
        return "RandomMowerRunningState::UNDEFINED";
    }
}

enum class MowerRunningState : int
{
    STOP = 0,
    RUNNING = 1,
    PAUSE = 2,
    UNDEFINED,
};

inline constexpr const char *asStringLiteral(const MowerRunningState state) noexcept
{
    switch (state)
    {
    case MowerRunningState::STOP:
        return "MowerRunningState::STOP";
    case MowerRunningState::RUNNING:
        return "MowerRunningState::RUNNING";
    case MowerRunningState::PAUSE:
        return "MowerRunningState::PAUSE";
    case MowerRunningState::UNDEFINED:
        return "MowerRunningState::UNDEFINED";
    }
    return "MowerRunningState::UNDEFINED";
}

using BeaconStatusPair = std::pair<Beaconstatus, Beaconstatus>;
struct BeaconStatusPairMap
{
    BeaconStatusPair beacon_status_pair;
    int mark_id;
    int beacon_look_count = 0; // Number of times the beacon is seen

    // Overload == operator
    bool operator==(const BeaconStatusPairMap &other) const
    {
        return this->mark_id == other.mark_id;
    }
};

struct BeaconStatus
{
    int mark_id = -1;
    int beacon_look_count = 0; // Number of times the beacon is seen

    BeaconStatus() = default;
    BeaconStatus &operator=(const BeaconStatus &) = default;
    BeaconStatus(int mark_id)
        : mark_id(mark_id)
    {
    }
    BeaconStatus(int mark_id, int beacon_look_count)
        : mark_id(mark_id)
        , beacon_look_count(beacon_look_count)
    {
    }
    BeaconStatus(const BeaconStatus &other)
        : mark_id(other.mark_id)
        , beacon_look_count(other.beacon_look_count)
    {
    }

    // Overload == operator
    bool operator==(const BeaconStatus &other) const
    {
        return this->mark_id == other.mark_id;
    }
};

// Grass detection status
enum class GrassDetectStatus : int
{
    NO_GRASS = 0,                // No grass (all obstacles)
    HAVE_GRASS_NO_OBSTACLE = 1,  // Grass with no obstacles (all grass)
    HAVE_GRASS_HAVE_OBSTACLE = 2 // Grass with obstacles (part grass, part obstacles)
};

enum class ObstacleDetectStatus : int
{
    NO_OBSTACLE = 0,  // No obstacle
    HAVE_OBSTACLE = 1 // Obstacle
};

enum class PositionOfGrass : int
{
    INVALID = -1,    // Invalid
    OUTER_GRASS = 0, // Outside grass
    INNER_GRASS = 1  // Inside grass
};

// Boundary direction enumeration
enum class BoundaryDirection : int
{
    NO_DIRECTION = -1,  // Invalid direction
    LEFT_DIRECTION = 0, // Direction towards the left
    RIGHT_DIRECTION = 1 // Direction towards the right
};

enum class BehaviorExceptionType : int
{
    SLIP = 0,
    COLLISION = 1,
    LIFTING = 2,
    STUCK = 3
};

// 0 means the current point is outside the grass, 1 means the current point is inside the grass, -1 means the result is invalid
// The correspondence between each member variable in the structure and the points in the image: one-to-one correspondence from left to right.
struct BoundaryStatus
{
    PositionOfGrass flag1{PositionOfGrass::INVALID};
    PositionOfGrass flag2{PositionOfGrass::INVALID};
    PositionOfGrass flag3{PositionOfGrass::INVALID};
    PositionOfGrass flag4{PositionOfGrass::INVALID};
};

struct FeatureSelectData
{
    ThreadControl alg_id{0};
    int alg_status{-1};
    // 当前触发的异常类型，用来通知behavior模块
    std::vector<BehaviorExceptionType> behavior_exception_types;
    bool is_recover_from_exception{false};
    bool is_behavior_loop{false};
    // 上一次触发的异常类型，用来通知各个功能模块，比如之前触发过打滑
    std::vector<BehaviorExceptionType> last_triggered_exception_types;

    FeatureSelectData()
    {
    }
    FeatureSelectData(ThreadControl alg_id, int alg_status)
        : alg_id(alg_id)
        , alg_status(alg_status)
    {
    }
    FeatureSelectData(const ThreadControl &alg_id, const int &alg_status, const std::vector<BehaviorExceptionType> &behavior_exception_types)
        : alg_id(alg_id)
        , alg_status(alg_status)
        , behavior_exception_types(behavior_exception_types)
    {
    }
};

// Distance boundary status
enum class BoundaryDistanceStatus : int
{
    CLOSE = 0,  // Close distance
    MIDDLE = 1, // Moderate distance
    FAR = 2     // Far distance
};

// Define a simple Point structure
struct Point
{
    int x, y;
    // Constructor
    Point(int x = 0, int y = 0)
        : x(x)
        , y(y)
    {
    }
};

// Define a structure representing a vector
struct Vector
{
    Point start; // Start point, the point closer to the mower is taken as the start point
    Point end;   // End point, the point farther from the mower is taken as the end point
};

struct Velocity2D
{
    float linear{0}; 
    float angular{0};
};

struct VelocityData
{
    float linear{0};      // Linear velocity
    float angular{0};     // Angular velocity
    uint64_t duration{0}; // Duration
};

struct InversePerspectImage
{
    uint32_t width;
    uint32_t height;
    uint32_t size;
    cv::Mat image;
    float pixels_to_meters;
    std::vector<cv::Point> path;
};

// Boundary detection result
struct BoundaryResult
{
    // Mask within the safety line after the mower's inverse perspective projection
    InversePerspectImage inverse_perspect_mask;
};

struct ObstacleResult
{
    // 0-No boundary detected, 1-Boundary detected
    ObstacleDetectStatus left_obstacle_status{ObstacleDetectStatus::NO_OBSTACLE};
    // 0-No boundary detected, 1-Boundary detected
    ObstacleDetectStatus ahead_obstacle_status{ObstacleDetectStatus::NO_OBSTACLE};
    // 0-No boundary detected, 1-Boundary detected
    ObstacleDetectStatus right_obstacle_status{ObstacleDetectStatus::NO_OBSTACLE};

    // Obstacle closest distance
    float obstacle_distance{0};
    // Control quantity
    VelocityData velocity_result;
};

struct OccupancyResult
{
    int16_t width = 0;
    int16_t height = 0;
    float resolution = 0;
    bool opt_status = false;
    std::vector<uint8_t> cells_array;
    std::vector<std::vector<uint8_t>> grid;
};

// Perception result
struct PerceptionFusionResult
{
    // 0-No grass detected, 1-Grass detected but no obstacles detected, 2-Grass and obstacles detected
    GrassDetectStatus grass_detecte_status{GrassDetectStatus::NO_GRASS};
    // Obstacle detection result
    ObstacleResult obstacle_result;
    // Boundary detection result
    BoundaryResult boundary_result;
    // Robot center coordinates (pixel coordinates in the image)
    Point mower_point;
    // Obstacle point closest to the robot
    Point min_dist_point;
    // Timestamp of the image input to the algorithm
    uint64_t input_timestamp;
    // Timestamp when the algorithm execution is completed
    uint64_t output_timestamp;
    // Occupancy grid
    OccupancyResult occupancy_grid;
};

enum class ChargeStationDirection : int
{
    LEFT = -1, // Left
    FRONT = 0, // Center
    RIGHT = 1  // Right
};

enum class ChargeStationPose : int
{
    LEFT = -1, // Left
    FRONT = 0, // Center
    RIGHT = 1  // Right
};

// Charging pile detection result
struct ChargeStationDetectResult
{
    uint64_t timestamp_ms{0};
    // Whether the charging pile is detected
    bool is_chargestation{false};
    // Whether the charging pile head is detected.
    bool is_head{false};
    // The offset direction of the charging pile relative to the camera (the rotation direction of the car), left, center, and right are -1, 0, 1 respectively
    ChargeStationDirection direction{ChargeStationDirection::FRONT};
    // The heading direction of the charging pile head, left: -1, center: 0, right: 1.
    ChargeStationPose pose{ChargeStationPose::FRONT};
    // The distance range of the target, the smaller the value, the closer the distance, currently there are 4 levels (0 1 2 3).
    int range{0};
    // The category id, confidence, and pixel coordinates of the upper left, lower right corners of the overall charging pile detection box (classID, conf, x1, y1, x2, y2).
    std::vector<float> station_box;
    // The category id, confidence, and pixel coordinates of the upper left, lower right corners of the charging pile head detection box (classID, conf, x1, y1, x2, y2).
    std::vector<float> head_box;
    // The offset direction of the overall charging pile pixel coordinates to the camera center.
    int charge_station_center_error;
    // The offset direction of the charging pile head pixel coordinates to the camera center.
    int head_center_error;

    void Reset()
    {
        is_head = false;
        is_chargestation = false;
    }
};

enum class QRCodeDetectStatus : int
{
    NO_DETECT_QRCODE = 0,       // QR code not detected
    DETECT_QRCODE_NO_POSE = 1,  // QR code detected, pose cannot be calculated
    DETECT_QRCODE_HAVE_POSE = 2 // Pose can be calculated
};

// QR code detection result
struct QRCodeLocationResult
{
    uint64_t timestamp_ms;                                                  // Timestamp
    int mark_perception_status;                                             // 0 means QR code is not perceived (detected); 1 means QR code is perceived (detected)
    int mark_perception_direction;                                          // -1 Left, the cross-region beacon is on the left side of the camera screen; 0 Center; 1 Right
    QRCodeDetectStatus detect_status{QRCodeDetectStatus::NO_DETECT_QRCODE}; // Mark pose detection status
    std::vector<std::pair<int, float>> v_markID_dis;                        // All detected markIDs and the distance from Mark 2 Camera in meters (m)
    int markID;                                                             // QR code ID to be (currently being) detected
    int target_direction;                                                   //-1 Left, need to turn left; 0 Center; 1 Right, need to turn right
    float quaternion_wxyz[4];                                               // Robot 2 Target rotation quaternion representation
    VecXYZRPW xyzrpw;                                                       // Euler angles
    void Reset()
    {
        detect_status = QRCodeDetectStatus::NO_DETECT_QRCODE;
        mark_perception_status = 0;
        v_markID_dis.clear();
    }
};

/**
 * @brief Current linear and angular velocity of the wheeled odometer car
 */
struct OdomResult
{
    float linear;
    float angular;
};

enum class MoveBehaviorStatus : int
{
    INVALID = 0,
    RUNNING = 1,
    SUCCESS = 2,
    FAILURE = 3,
    NEXT_BEHAVIOR = 4
};

enum class MoveBehaviorType : int
{
    INVALID = 0,
    SLIP_BACK = 1,
    SLIP_FORWARD = 2,
    SLIP_TURN = 3,
    LIFT_BACK = 4,
    LIFT_FORWARD = 5,
    LIFT_TURN = 6,
    COLLISION_BACK = 7,
    COLLISION_FORWARD = 8,
    COLLISION_TURN = 9,
    STUCK_TURN_LEFT = 10,
    STUCK_TURN_RIGHT = 11,
    STUCK_BACKFORWARD = 12,
    STUCK_FORWARD = 13,
    STUCK_SINGLE_WHEEL_TURN_LEFT = 14,
    STUCK_SINGLE_WHEEL_TURN_RIGHT = 15,
};

/**
 * @brief Detect the beacon and its distance to the camera
 */
struct MarkIdDistance
{
    int mark_id;
    float distance;
};

/*
detect_status:
    0, QR code not detected
    1, QR code detected, pose cannot be calculated
    2  Pose can be calculated
mark_perception_status:
    0 means the beacon is not perceived (detected); 1 means the beacon is perceived (detected)
mark_perception_direction:
    -1 Left, the cross-region beacon is on the left side of the camera screen; 0 Center; 1 Right
roi_confidence:
    -1 means detection failed
    0 means not in the ROI area
    1 means in the ROI area

    Confidence when the beacon is detected: 0~100, the higher the value, the higher the confidence, the closer the mark point is to the center of the image
target_direction:
    -1 means detection failed; 0 Turn left; 1 Turn right
mark_id:
    Cone ID being detected
mark_id_distance:
    All detected markIDs and the distance from Mark 2 Camera in meters (m)
xyzrpw:
    X, Y, Z, ROLL, PITCH, YAW
*/
struct MarkLocationResult
{
    uint64_t timestamp; // ms
    int detect_status;
    int mark_perception_status;
    int mark_perception_direction;
    int roi_confidence;
    int target_direction;
    int mark_id;
    std::vector<MarkIdDistance> mark_id_distance;
    VecXYZRPW xyzrpw;
    void Reset()
    {
        detect_status = 0;
        mark_perception_status = 0;
        roi_confidence = -1;
        target_direction = -1;
        mark_id = 1;
        mark_id_distance.clear();
    }
};

// X-axis acceleration (m/s2), float type
// Y-axis acceleration (m/s2), float type
// Z-axis acceleration (m/s2), float type
// X-axis angular velocity (rad/s), float type
// Y-axis angular velocity (rad/s), float type
// Z-axis angular velocity (rad/s), float type
struct ImuData
{
    uint64_t frame_timestamp; // Unit: milliseconds, uint64_t type
    uint64_t system_timestamp;
    float linear_acceleration_x; //
    float linear_acceleration_y;
    float linear_acceleration_z;
    float angular_velocity_x;
    float angular_velocity_y;
    float angular_velocity_z;
};

// Left motor speed, need to be divided by 99.5 to get the actual left motor speed, unit: RPM (revolutions per minute), float type
// Right motor speed, need to be divided by 99.5 to get the actual left motor speed, unit: RPM (revolutions per minute), float type
struct MotorSpeedData
{
    uint64_t frame_timestamp; // Unit: milliseconds, uint64_t type
    uint64_t system_timestamp;
    float motor_speed_left;
    float motor_speed_right;
    float current_left;  // Left motor current (amperes)
    float current_right; // Right motor current (amperes)
};

struct MotionDetectionResult
{
    double ave_pix_diff;
    uint64_t timestamp; // unit: ms
    bool is_motion;
};

struct Point2i
{
    Point2i(int _x, int _y)
        : x(_x)
        , y(_y)
    {
    }

    int x;
    int y;
};

struct Point2f
{
    Point2f(float _x, float _y)
        : x(_x)
        , y(_y)
    {
    }

    float x;
    float y;
};

struct Pose2f
{
    Pose2f() = default;
    Pose2f(float _x, float _y, float _theta)
        : x(_x)
        , y(_y)
        , theta(_theta)
    {
    }

    float x;
    float y;
    float theta;
};

struct CollisionInfo
{
    bool is_collision = false;
    float collision_dist = 0;
};

struct TrajectoryPose
{
    TrajectoryPose() = default;
    TrajectoryPose(double _x, double _y, double _theta, double _linear_velocity, double _angular_velocity)
        : x(_x)
        , y(_y)
        , theta(_theta)
        , linear_velocity(_linear_velocity)
        , angular_velocity(_angular_velocity)
    {
    }
    TrajectoryPose(double _x, double _y, double _theta)
        : x(_x)
        , y(_y)
        , theta(_theta)
        , linear_velocity(0)
        , angular_velocity(0)
    {
    }

    double x;
    double y;
    double theta;
    double linear_velocity;
    double angular_velocity;
};

} // namespace fescue_iox

#endif
