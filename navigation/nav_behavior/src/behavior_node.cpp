#include "behavior_node.hpp"

#include "behavior.hpp"
#include "behavior_node_config.hpp"
#include "mower_sdk_version.h"
#include "nav_utils.hpp"
#include "process_fusion.hpp"
#include "utils/dir.hpp"
#include "utils/logger.hpp"
#include "utils/rate.hpp"
#include "utils/time.hpp"
#include "utils/utils.hpp"
#include "yaml-cpp/yaml.h"

#include <algorithm>
#include <chrono>
#include <cmath>      // for std::labs()
#include <filesystem> //c++17
#include <limits>
#include <memory>
#include <string>
#include <utility>
#include <vector>

namespace fescue_iox
{

NavigationBehaviorNode::NavigationBehaviorNode(const std::string &node_name)
    : node_name_(node_name)
{
    InitWorkingDirectory();
    InitParam();
    InitLogger();
    InitSubscriber();
    InitPublisher();
    InitService();
    InitAlgorithm();
    InitHeartbeat();
}

NavigationBehaviorNode::~NavigationBehaviorNode()
{
    DeinitAlgorithm();
    LOG_WARN("NavigationBehaviorNode exit!");
}

void NavigationBehaviorNode::InitWorkingDirectory()
{
    std::string working_directory = SetWorkingDirectory("/../");
    LOG_INFO("{} working directory is: {}", node_name_.c_str(), working_directory.c_str());
}

void NavigationBehaviorNode::InitParam()
{
    const std::string conf_file{"conf/navigation_behavior_node/navigation_behavior_node.yaml"};
    std::string conf_path = GetDirectoryPath(conf_file);
    if (!conf_path.empty())
    {
        LOG_INFO("NavigationBehaviorNode create config path: {}", conf_path.c_str());
        if (!CreateDirectories(conf_path))
        {
            LOG_ERROR("NavigationBehaviorNode create config path failed!!!");
        }
    }
    if (!Config<NavigationBehaviorNodeConfig>::Init(conf_file))
    {
        LOG_WARN("Init NavigationBehaviorNode config parameters failed!");
    }
    NavigationBehaviorNodeConfig config = Config<NavigationBehaviorNodeConfig>::GetConfig();

    LOG_INFO("[navigation_behavior_node] git tag: {}", _GIT_TAG_);
    LOG_INFO("[navigation_behavior_node] git version: {}", _GIT_VERSION_);
    LOG_INFO("[navigation_behavior_node] compile time: {}", _COMPILE_TIME_);

    LOG_INFO("{}", config.toString().c_str());

    log_dir_ = config.common_conf.log_dir;
    console_log_level_ = config.common_conf.console_log_level;
    file_log_level_ = config.common_conf.file_log_level;
    behavior_alg_conf_file_ = config.behavior_alg_conf_file;

    if (!Config<NavigationBehaviorNodeConfig>::SetConfig(config, true))
    {
        LOG_WARN("Set NavigationBehaviorNode config parameters failed!");
    }

    CreateDirectories(log_dir_);
}

void NavigationBehaviorNode::InitAlgorithmParam()
{
}

void NavigationBehaviorNode::InitAlgorithm()
{
    InitAlgorithmParam();

    behavior_alg_ = std::make_unique<NavigationBehaviorAlg>(behavior_param_);
    behavior_alg_->SetBehaviorRunningStateCallback([this](BehaviorRunningState state, bool is_exception_loop, const std::vector<BehaviorExceptionType>& exception_types) {
        this->DealBehaviorRunningStateCallback(state, is_exception_loop, exception_types);
    });

    thread_running_.store(true);
    behavior_thread_ = std::thread(&NavigationBehaviorNode::BehaviorThread, this);
}

void NavigationBehaviorNode::DeinitAlgorithm()
{
    thread_running_.store(false);
    if (behavior_thread_.joinable())
    {
        behavior_thread_.join();
    }
}

void NavigationBehaviorNode::InitLogger()
{
    std::string log_file_name = log_dir_ + "/" + node_name_ + ".log";
    SpdlogParams params(node_name_, console_log_level_, file_log_level_, log_file_name);
    InitSpdlogParams(params);
}

void NavigationBehaviorNode::InitHeartbeat()
{
    pub_heartbeat_ = std::make_unique<NodeHeartbeatPublisher>();
    pub_heartbeat_->start();
}

void NavigationBehaviorNode::DealBehaviorRunningStateCallback(BehaviorRunningState state, bool is_exception_loop, const std::vector<BehaviorExceptionType>& exception_types)
{
    if (pub_behavior_state_)
    {
        LOG_INFO("behavior_state:({}) is_exception_loop: {} exception_types size: {}", static_cast<int>(state), is_exception_loop, exception_types.size());
        fescue_msgs__msg__BehaviorStateData data;
        data.state = static_cast<int>(state);
        data.is_loop = is_exception_loop;
        for (const auto &exception_type : exception_types)
        {
            LOG_INFO("exception_type: {}", static_cast<int>(exception_type));
            data.exception_types.push_back(static_cast<int>(exception_type));
        }
        pub_behavior_state_->publish(data);
    }
}

void NavigationBehaviorNode::SetMotorSpeedData(const MotorSpeedData &motor_speed_data)
{
    if (behavior_alg_)
    {
        behavior_alg_->SetMotorSpeedData(motor_speed_data);
    }
}

void NavigationBehaviorNode::SetMotionDetectionResult(const MotionDetectionResult &motion_detection_result)
{
    if (behavior_alg_)
    {
        behavior_alg_->SetMotionDetectionResult(motion_detection_result);
    }
}

void NavigationBehaviorNode::InitSubscriber()
{
    sub_fusion_result_ = std::make_unique<IceoryxSubscriberMower<fescue_msgs__msg__PerceptionFusionResult>>(
        "fusion_result", 1, [this](const fescue_msgs__msg__PerceptionFusionResult &data, const std::string &event) {
            (void)event;
            DealPerceptionFusionResult(data);
        });
    sub_nav_alg_ctrl_ = std::make_unique<IceoryxSubscriberMower<fescue_msgs__msg__NavigationAlgoCtrlData>>(
        "navigation_nav_alg_ctrl", 10, [this](const fescue_msgs__msg__NavigationAlgoCtrlData &data, const std::string &event) {
            (void)event;
            DealNavAlgCtrlResult(data);
        });
    sub_nav_running_state_ = std::make_unique<IceoryxSubscriberMower<fescue_msgs__msg__NavigationRunningStateData>>(
        "navigation_running_state", 10, [this](const fescue_msgs__msg__NavigationRunningStateData &data, const std::string &event) {
            (void)event;
            DealNavRunningState(data);
        });
    sub_mcu_exception_ = std::make_unique<IceoryxSubscriberMower<mower_msgs::msg::McuException>>(
        "mcu_exception", 10, [this](const mower_msgs::msg::McuException &data, const std::string &event) {
            (void)event;
            DealMCUException(data);
        });
#if 0
    sub_mcu_imu_ = std::make_unique<IceoryxSubscriberMower<mower_msgs::msg::McuImu>>(
        "mcu_imu", 1, [this](const mower_msgs::msg::McuImu &data, const std::string &event) {
            (void)event;
            DealMcuImu(data);
        });
#else
    sub_soc_imu_ = std::make_unique<IceoryxSubscriberMower<mower_msgs::msg::SocImu>>(
        "soc_imu", 1, [this](const mower_msgs::msg::SocImu &data, const std::string &event) {
            (void)event;
            DealSocImu(data);
        });
#endif
    sub_mcu_motor_speed_ = std::make_unique<IceoryxSubscriberMower<mower_msgs::msg::McuMotorSpeed>>(
        "mcu_motor_speed", 1, [this](const mower_msgs::msg::McuMotorSpeed &data, const std::string &event) {
            (void)event;
            DealMcuMotorSpeed(data);
        });

    sub_motion_detection_result_ = std::make_unique<IceoryxSubscriberMower<fescue_msgs__msg__LocalizationMotionDetectionResult>>(
        "localization_motion_detection_result", 1, [this](const fescue_msgs__msg__LocalizationMotionDetectionResult &data, const std::string &event) {
            (void)event;
            DealMotionDetectionResult(data);
        });

    sub_nav_fusion_pose_ = std::make_unique<IceoryxSubscriberMower<ob_mower_msgs::NavFusionPose>>(
        "navigation_fusion_pose", 1, [this](const ob_mower_msgs::NavFusionPose &data, const std::string &event) {
            (void)event;
            DealNavFusionPose(data);
        });
    sub_function_state_ = std::make_unique<IceoryxSubscriberMower<fescue_msgs_FunctionStateData>>(
        "navigation_function_state", 10, [this](const fescue_msgs_FunctionStateData &data, const std::string &event) {
            (void)event;
            DealFunctionState(data);
        });
}

void NavigationBehaviorNode::InitPublisher()
{
    pub_behavior_state_ = std::make_unique<IceoryxPublisherMower<fescue_msgs__msg__BehaviorStateData>>("navigation_behavior_state");
    pub_behavior_final_result_ = std::make_unique<IceoryxPublisherMower<fescue_msgs__msg__NavBehaviorFinalResult>>("navigation_behavior_final_result");
    pub_iceoryx_exception_ = std::make_unique<IceoryxPublisherMower<mower_msgs::msg::SocException>>("soc_exception");
    pub_function_state_ = std::make_unique<IceoryxPublisherMower<fescue_msgs_FunctionStateData>>("navigation_function_state");
}

void NavigationBehaviorNode::InitService()
{
    service_get_node_param_ = std::make_unique<IceoryxServerMower<get_node_param_request, get_node_param_response>>(
        "get_navigation_behavior_node_param_request", 10U,
        [this](const get_node_param_request &request, get_node_param_response &response) {
            (void)request;
            response.success = GetBehaviorNodeParam(response.data);
            LOG_INFO("Get navigation behavior node param execute {}", response.success);
        });

    service_set_node_param_ = std::make_unique<IceoryxServerMower<set_node_param_request, set_node_param_response>>(
        "set_navigation_behavior_node_param_request", 10U,
        [this](const set_node_param_request &request, set_node_param_response &response) {
            response.success = SetBehaviorNodeParam(request.data);
            LOG_INFO("Set navigation behavior node param execute {}", response.success);
        });

    service_get_alg_param_ = std::make_unique<IceoryxServerMower<get_alg_param_request, get_alg_param_response>>(
        "get_navigation_behavior_alg_param_request", 10U,
        [this](const get_alg_param_request &request, get_alg_param_response &response) {
            (void)request;
            response.success = GetBehaviorAlgParam(response.data);
            LOG_INFO("Get navigation behavior alg param execute {}", response.success);
        });

    service_set_alg_param_ = std::make_unique<IceoryxServerMower<set_alg_param_request, set_alg_param_response>>(
        "set_navigation_behavior_alg_param_request", 10U,
        [this](const set_alg_param_request &request, set_alg_param_response &response) {
            response.success = SetBehaviorAlgParam(request.data);
            LOG_INFO("Set navigation behavior alg param execute {}", response.success);
        });
}

void NavigationBehaviorNode::CheckMCUExeceptionTimeout()
{
    std::lock_guard<std::mutex> lock(mcu_exception_mutex_);
    auto now = std::chrono::steady_clock::now();
    auto duration = std::chrono::duration_cast<std::chrono::milliseconds>(now - last_mcu_exception_time_).count();
    if (duration > 100)
    {
        mcu_exception_status_ = McuExceptionStatus::NORMAL;
        LOG_WARN_THROTTLE(5000, "[BehaviorThread] MCUException data timeout: {} ms, status reset to NORMAL", duration);
    }
}

void NavigationBehaviorNode::BehaviorThread()
{
    PerceptionFusionResult fusion_result;
    McuExceptionStatus mcu_exception_status;
    ImuData imu_data;
    MotorSpeedData motor_speed_data;
    MotionDetectionResult motion_detection_result;

    while (thread_running_.load())
    {
        bool behavior_enable = false;
        std::vector<BehaviorExceptionType> behavior_exception_types;
        {
            std::scoped_lock lock(behavior_mutex_);
            behavior_enable = behavior_enable_.load();
            behavior_exception_types = behavior_exception_types_;
            behavior_exception_types_.clear();
        }
        if (behavior_enable)
        {
            CheckMCUExeceptionTimeout();
            {
                std::scoped_lock lock(fusion_mutex_, mcu_exception_mutex_, imu_mtx_, motor_speed_mtx_,
                                      motion_detection_result_mtx_);
                fusion_result = fusion_result_;
                mcu_exception_status = mcu_exception_status_;
                imu_data = imu_data_;
                motor_speed_data = motor_speed_data_;
                motion_detection_result = motion_detection_result_;
            }
            if (behavior_alg_)
            {
                auto result = behavior_alg_->DoBehavior(fusion_result,
                                                        mcu_exception_status,
                                                        imu_data,
                                                        motor_speed_data,
                                                        motion_detection_result,
                                                        behavior_exception_types);
                if (result.behavior_status == BehaviorStatus::Failed)
                {
                    LOG_INFO("behavior status failed, publish error");
                    mower_msgs::msg::SocException exception;
                    exception.node_name = "navigation_behavior_node";
                    exception.exception_level = mower_msgs::msg::SocExceptionLevel::ERROR;
                    exception.exception_value = mower_msgs::msg::SocExceptionValue::ALG_PNC_STRONG_RECOVERY_MODE_FAILED_ERROR;
                    pub_iceoryx_exception_->publishAtInterval(exception, std::chrono::milliseconds{1000});
                }
                auto mower_running_state = behavior_alg_->GetMowerRunningState();
                if (mower_running_state == MowerRunningState::RUNNING)
                {
                    PublishFunctionState();
                }
                if (result.behavior_completed)
                {
                    LOG_INFO("BehaviorThread completed is {}!", result.behavior_completed);
                    PublishBehaviorFinalResult(result);
                    {
                        std::scoped_lock lock(behavior_mutex_);
                        behavior_enable_.store(false);
                        behavior_exception_types_.clear();
                    }
                    behavior_alg_->ResetBehaviorFlags();
                }
            }
        }
        else
        {
            LOG_DEBUG_THROTTLE(2000, "NavigationBehaviorAlg disable!");
            if (behavior_alg_)
            {
                behavior_alg_->ResetBehaviorFlags();
            }
        }
        std::this_thread::sleep_for(std::chrono::milliseconds(20));
    }
}

void NavigationBehaviorNode::DealPerceptionFusionResult(const fescue_msgs__msg__PerceptionFusionResult &msg)
{
    // switch (msg.boundary_state)
    // {
    // case 0:
    //     LOG_DEBUG("[BehaviorNode] [fusion_result] No grass");
    //     break;
    // case 1:
    //     LOG_DEBUG("[BehaviorNode] [fusion_result] All grass");
    //     break;
    // case 2:
    //     LOG_DEBUG("[BehaviorNode] [fusion_result] Some grass and some obstacles");
    //     break;
    // default:
    //     LOG_DEBUG("Invalid perception grass detect status: {}", int(msg.boundary_state));
    //     break;
    // }

    std::lock_guard<std::mutex> lock(fusion_mutex_);
    GetFusionGrassDetectStatus(msg, fusion_result_.grass_detecte_status);
    GetFusionObstacleResult(msg, fusion_result_.obstacle_result);
    GetFusionBoundaryResult(msg, fusion_result_.boundary_result);
    fusion_result_.mower_point.x = msg.mower_point.x;
    fusion_result_.mower_point.y = msg.mower_point.y;
    fusion_result_.min_dist_point.x = msg.min_dist_point.x;
    fusion_result_.min_dist_point.y = msg.min_dist_point.y;
    fusion_result_.input_timestamp = msg.timestamp;
    fusion_result_.output_timestamp = msg.output_timestamp;
}

void NavigationBehaviorNode::DealNavAlgCtrlResult(const fescue_msgs__msg__NavigationAlgoCtrlData &msg)
{
    for (size_t i = 0; i < msg.data.size(); i++)
    {
        const auto &cur_types = msg.data[i].behavior_exception_types;
        if (msg.data[i].type == FESCUE_MSGS_ENUM_NAV_ALGO_TYPE_BEHAVIOR)
        {
            bool origin_behavior_enable;
            // LOG_INFO("NavigationBehaviorNode ctrl result: {} sender: {}", msg.data[i].state, msg.sender.c_str());
            switch (msg.data[i].state)
            {
            case FESCUE_MSGS_ENUM_NAV_ALGO_STATE_DISABLE:
            {
                std::scoped_lock lock(behavior_mutex_);
                behavior_enable_.store(false);
                behavior_exception_types_.clear();
            }
                SetBehaviorVelPublisherProhibit(true); // Turn on prohibit to publish speed
                break;
            case FESCUE_MSGS_ENUM_NAV_ALGO_STATE_ENABLE:
            {
                std::scoped_lock lock(behavior_mutex_);
                origin_behavior_enable = behavior_enable_.load();
                behavior_enable_.store(true);
                behavior_exception_types_.clear();
                for (size_t j = 0; j < cur_types.size(); j++)
                {
                    behavior_exception_types_.push_back(static_cast<BehaviorExceptionType>(cur_types[j]));
                }
                if (!origin_behavior_enable)
                {
                    behavior_alg_->ResetStatus();
                }
            }
                SetBehaviorVelPublisherProhibit(false); // Turn off prohibit to publish speed
                break;
            default:
                break;
            }
            break;
        }
    }
}

void NavigationBehaviorNode::DealFunctionState(const fescue_msgs_FunctionStateData &data)
{
    const auto &function_state = data.state;
    if (behavior_alg_)
    {
        behavior_alg_->SetFunctionState(function_state);
    }
}

void NavigationBehaviorNode::DealNavRunningState(const fescue_msgs__msg__NavigationRunningStateData &data)
{
    if (behavior_alg_)
    {
        behavior_alg_->SetAlgoRunningState(static_cast<MowerRunningState>(data.state));
    }
}

void NavigationBehaviorNode::DealMCUException(const mower_msgs::msg::McuException &data)
{
    std::lock_guard<std::mutex> lock(mcu_exception_mutex_);

    last_mcu_exception_time_ = std::chrono::steady_clock::now();

    switch (data.exception_value)
    {
    case mower_msgs::msg::McuExceptionValue::COLLISION_BELOW_3_SECOND_EXCEPTION: // collision below 3 seconds Action: Retreat, turn to continue
    {
        LOG_DEBUG("[DealMCUException1] collision below 3 seconds Action: Retreat, turn to continue");
        mcu_exception_status_ = McuExceptionStatus::COLLISION;
        break;
    }

    case mower_msgs::msg::McuExceptionValue::COLLISION_ABOVE_3_SECOND_EXCEPTION: // collision above 3 seconds Action: The traveling motor retreats and turns to perform obstacle avoidance
    {
        LOG_DEBUG("[DealMCUException1] collision above 3 seconds Action: The traveling motor retreats and turns to perform obstacle avoidance");
        mcu_exception_status_ = McuExceptionStatus::COLLISION;
        break;
    }

    case mower_msgs::msg::McuExceptionValue::SINGLE_LIFT_SENSOR_TRIGGER_0_3_TO_3_SECONDS_EXCEPTION: // single lift sensor trigger 0.3-3 seconds Action: The walking motor continues to maintain its original state
    {
        LOG_DEBUG("[DealMCUException1] single lift sensor trigger 0.3-3 seconds Action: The walking motor continues to maintain its original state");
        mcu_exception_status_ = McuExceptionStatus::NORMAL;
        break;
    }

    case mower_msgs::msg::McuExceptionValue::SINGLE_LIFT_SENSOR_TRIGGER_3_TO_10_SECONDS_EXCEPTION: // single lift sensor trigger 3-10 seconds Action: Stop the mowing motor, and the traveling motor retreats to try to release the lift
    {
        LOG_DEBUG("[DealMCUException1] single lift sensor trigger 3-10 seconds Action: Stop the mowing motor, and the traveling motor retreats to try to release the lift");
        mcu_exception_status_ = McuExceptionStatus::LIFTING;
        break;
    }

    case mower_msgs::msg::McuExceptionValue::DOUBLE_LIFT_SENSOR_TRIGGER_BELOW_10_SECONDS_EXCEPTION: // double lift sensor trigger below 10 seconds Action: Stop the mowing motor, and the traveling motor retreats to try to release the lift
    {
        LOG_DEBUG("[DealMCUException1] double lift sensor trigger below 10 seconds Action: Stop the mowing motor, and the traveling motor retreats to try to release the lift");
        mcu_exception_status_ = McuExceptionStatus::LIFTING;
        break;
    }

    case mower_msgs::msg::McuExceptionValue::NO_EXCEPTION:
    {
        LOG_DEBUG("[DealMCUException1] no exception");
        mcu_exception_status_ = McuExceptionStatus::NORMAL;
        break;
    }

    default:
        LOG_DEBUG("[DealMCUException1] No safety abnormalities");
        mcu_exception_status_ = McuExceptionStatus::UNKNOWN;
        break;
    }
}

void NavigationBehaviorNode::DealMcuImu(const mower_msgs::msg::McuImu &data)
{
    std::lock_guard<std::mutex> lock(imu_mtx_);

    if (enable_imu_filter_)
    {
        filter_state_.accel_x = LowPassFilter(data.linear_acceleration_x, filter_state_.accel_x, alpha_imu_);
        filter_state_.accel_y = LowPassFilter(data.linear_acceleration_y, filter_state_.accel_y, alpha_imu_);
        filter_state_.accel_z = LowPassFilter(data.linear_acceleration_z, filter_state_.accel_z, alpha_imu_);
        filter_state_.gyro_x = LowPassFilter(data.angular_velocity_x, filter_state_.gyro_x, alpha_imu_);
        filter_state_.gyro_y = LowPassFilter(data.angular_velocity_y, filter_state_.gyro_y, alpha_imu_);
        filter_state_.gyro_z = LowPassFilter(data.angular_velocity_z, filter_state_.gyro_z, alpha_imu_);

        imu_data_.linear_acceleration_x = filter_state_.accel_x;
        imu_data_.linear_acceleration_y = filter_state_.accel_y;
        imu_data_.linear_acceleration_z = filter_state_.accel_z;
        imu_data_.angular_velocity_x = filter_state_.gyro_x;
        imu_data_.angular_velocity_y = filter_state_.gyro_y;
        imu_data_.angular_velocity_z = filter_state_.gyro_z;
        imu_data_.frame_timestamp = data.frame_timestamp;
        imu_data_.system_timestamp = data.system_timestamp;
    }
    else
    {
        imu_data_.angular_velocity_x = data.angular_velocity_x;
        imu_data_.angular_velocity_y = data.angular_velocity_y;
        imu_data_.angular_velocity_z = data.angular_velocity_z;
        imu_data_.linear_acceleration_x = data.linear_acceleration_x;
        imu_data_.linear_acceleration_y = data.linear_acceleration_y;
        imu_data_.linear_acceleration_z = data.linear_acceleration_z;
        imu_data_.frame_timestamp = data.frame_timestamp;
        imu_data_.system_timestamp = data.system_timestamp;
    }
}

void NavigationBehaviorNode::DealSocImu(const mower_msgs::msg::SocImu &data)
{
    std::lock_guard<std::mutex> lock(imu_mtx_);

    if (enable_imu_filter_)
    {
        filter_state_.accel_x = LowPassFilter(data.linear_acceleration_x, filter_state_.accel_x, alpha_imu_);
        filter_state_.accel_y = LowPassFilter(data.linear_acceleration_y, filter_state_.accel_y, alpha_imu_);
        filter_state_.accel_z = LowPassFilter(data.linear_acceleration_z, filter_state_.accel_z, alpha_imu_);
        filter_state_.gyro_x = LowPassFilter(data.angular_velocity_x, filter_state_.gyro_x, alpha_imu_);
        filter_state_.gyro_y = LowPassFilter(data.angular_velocity_y, filter_state_.gyro_y, alpha_imu_);
        filter_state_.gyro_z = LowPassFilter(data.angular_velocity_z, filter_state_.gyro_z, alpha_imu_);

        imu_data_.linear_acceleration_x = filter_state_.accel_x;
        imu_data_.linear_acceleration_y = filter_state_.accel_y;
        imu_data_.linear_acceleration_z = filter_state_.accel_z;
        imu_data_.angular_velocity_x = filter_state_.gyro_x;
        imu_data_.angular_velocity_y = filter_state_.gyro_y;
        imu_data_.angular_velocity_z = filter_state_.gyro_z;
        imu_data_.frame_timestamp = data.frame_timestamp;
        imu_data_.system_timestamp = data.system_timestamp;
    }
    else
    {
        imu_data_.angular_velocity_x = data.angular_velocity_x;
        imu_data_.angular_velocity_y = data.angular_velocity_y;
        imu_data_.angular_velocity_z = data.angular_velocity_z;
        imu_data_.linear_acceleration_x = data.linear_acceleration_x;
        imu_data_.linear_acceleration_y = data.linear_acceleration_y;
        imu_data_.linear_acceleration_z = data.linear_acceleration_z;
        imu_data_.frame_timestamp = data.frame_timestamp;
        imu_data_.system_timestamp = data.system_timestamp;
    }
}

float NavigationBehaviorNode::LowPassFilter(float new_value, float prev_value, float alpha)
{
    return alpha * new_value + (1.0f - alpha) * prev_value;
}

void NavigationBehaviorNode::PublishFunctionState()
{
    if (pub_function_state_)
    {
        fescue_msgs_FunctionStateData function_state_data;
        function_state_data.state = fescue_msgs_enum_FunctionState::FUNCTION_STATE_BEHAVIOR;
        pub_function_state_->publish(function_state_data);
    }
}

void NavigationBehaviorNode::DealMcuMotorSpeed(const mower_msgs::msg::McuMotorSpeed &data)
{
    std::lock_guard<std::mutex> lock(motor_speed_mtx_);

    if (enable_motor_speed_filter_)
    {
        filter_state_.motor_speed_left = LowPassFilter(data.motor_speed_left, filter_state_.motor_speed_left, alpha_speed_);
        filter_state_.motor_speed_right = LowPassFilter(data.motor_speed_right, filter_state_.motor_speed_right, alpha_speed_);
        motor_speed_data_.motor_speed_left = filter_state_.motor_speed_left;
        motor_speed_data_.motor_speed_right = filter_state_.motor_speed_right;
        motor_speed_data_.frame_timestamp = data.frame_timestamp;
        motor_speed_data_.system_timestamp = data.system_timestamp;

        // motor_speed_data_.current_left = data.current_left; // current
        // motor_speed_data_.current_right = data.current_right;

        SetMotorSpeedData(motor_speed_data_);
    }
    else
    {
        motor_speed_data_.motor_speed_left = data.motor_speed_left;
        motor_speed_data_.motor_speed_right = data.motor_speed_right;
        motor_speed_data_.frame_timestamp = data.frame_timestamp;
        motor_speed_data_.system_timestamp = data.system_timestamp;
        // update encoder datas

        SetMotorSpeedData(motor_speed_data_);
    }
}

void NavigationBehaviorNode::DealMotionDetectionResult(const fescue_msgs__msg__LocalizationMotionDetectionResult &data)
{
    std::lock_guard<std::mutex> lock(motion_detection_result_mtx_);
    motion_detection_result_.ave_pix_diff = data.ave_pix_diff;
    motion_detection_result_.is_motion = data.is_motion;
    motion_detection_result_.timestamp = data.timestamp_ms;
    // LOG_ERROR("[DealMotionDetectionResult] ave_pix_diff: {}, is_motion: {}", data.ave_pix_diff, data.is_motion);
    // LOG_ERROR("[DealMotionDetectionResult] timestamp: {}", data.timestamp_ms);

    SetMotionDetectionResult(motion_detection_result_);
}

void NavigationBehaviorNode::DealNavFusionPose(const ob_mower_msgs::NavFusionPose &data)
{
    if (behavior_alg_)
    {
        behavior_alg_->SetFusionPose(data);
    }
}

bool NavigationBehaviorNode::GetBehaviorAlgParam(fescue_msgs__msg__NavigationBehaviorAlgParam &data)
{
    (void)data;
    if (!behavior_alg_)
    {
        return false;
    }
    BehaviorAlgParam param;
    return true;
}

bool NavigationBehaviorNode::SetBehaviorAlgParam(const fescue_msgs__msg__NavigationBehaviorAlgParam &data)
{
    (void)data;
    if (!behavior_alg_)
    {
        return false;
    }
    return true;
}

bool NavigationBehaviorNode::GetBehaviorNodeParam(ob_mower_srvs::NodeParamData &data)
{
    data.console_log_level.unsafe_assign(console_log_level_.c_str());
    data.file_log_level.unsafe_assign(file_log_level_.c_str());
    return true;
}

bool NavigationBehaviorNode::SetBehaviorNodeParam(const ob_mower_srvs::NodeParamData &data)
{
    console_log_level_ = std::string(data.console_log_level.c_str());
    file_log_level_ = std::string(data.file_log_level.c_str());
    InitLogger();
    NavigationBehaviorNodeConfig config = Config<NavigationBehaviorNodeConfig>::GetConfig();
    config.common_conf.console_log_level = console_log_level_;
    config.common_conf.file_log_level = file_log_level_;
    if (!Config<NavigationBehaviorNodeConfig>::SetConfig(config))
    {
        LOG_WARN("Set NavigationBehaviorNode config parameters failed!");
        return false;
    }
    LOG_INFO("New NavigationBehaviorNode params: {}", config.toString().c_str());
    return true;
}

void NavigationBehaviorNode::PublishBehaviorFinalResult(const BehaviorAlgResult &result)
{
    if (pub_behavior_final_result_)
    {
        fescue_msgs__msg__NavBehaviorFinalResult final_result;
        final_result.completed = result.behavior_completed;
        final_result.timestamp = GetSteadyClockTimestampMs();
        if (result.behavior_status == BehaviorStatus::Successed)
        {
            final_result.result = true;
        }
        else if (result.behavior_status == BehaviorStatus::Failed)
        {
            final_result.result = false;
        }
        pub_behavior_final_result_->publish(final_result);
    }
}

} // namespace fescue_iox
