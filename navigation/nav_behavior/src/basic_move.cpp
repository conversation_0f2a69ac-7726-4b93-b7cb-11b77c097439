#include "basic_move.hpp"

#include "utils/logger.hpp"
#include "utils/math_type.hpp"
#include "utils/time.hpp"

namespace fescue_iox
{

MoveBehaviorStatus BasicMove::Process(const Pose2f& pose, const BehaviorException& exception, 
                                      const PerceptionFusionResult &fusion_result, 
                                      const AvoidObsMode& avoid_obs_mode)
{
    if (exception.is_exception_loop || exception.is_slip)
    {
        if (!is_exception_loop_)
        {
            LOG_INFO("set exception loop name: {}", GetName());
        }
        is_exception_loop_ = true;
    }
    InitData(fusion_result, avoid_obs_mode);
    auto exception_behavior_type = ProcessException(exception);
    if (exception_behavior_type != MoveBehaviorType::INVALID)
    {
        LOG_INFO("exception behavior, return NEXT_BEHAVIOR, type: {}", static_cast<int>(exception_behavior_type));
        next_behavior_type_ = exception_behavior_type;
        return MoveBehaviorStatus::NEXT_BEHAVIOR;
    }
    MoveStatus move_status = MoveStatus::INVALID;
    if (!is_behavior_inited_) 
    {
        is_behavior_inited_ = true;
        start_pose_ = pose;
        start_time_ = GetSteadyClockTimestampMs();
        LOG_INFO("behavior inited, type: {}, start_pose x: {}, y: {}, theta: {} is_turn: {}", 
                  GetName(), start_pose_.x, start_pose_.y, start_pose_.theta, config_.is_turn);
    }
    if (IsTimeout())
    {
        LOG_ERROR("behavior timeout, return FAILURE");
        return MoveBehaviorStatus::FAILURE;
    }
    auto success_behavior_type = success_behavior_type_;
    if (IsFinished(pose))
    {
        move_status = MoveStatus::SUCCESS;
        success_behavior_type = MoveBehaviorType::INVALID;
    }
    else if (config_.is_turn)
    {
        move_status = ProcessTurn(pose);
    }
    else
    {
        move_status = ProcessMove(pose);
    }
    if (move_status == MoveStatus::SUCCESS || move_status == MoveStatus::FAILURE)
    {
        ProcessBehaviorSuccess(exception);
        if (success_behavior_type == MoveBehaviorType::INVALID)
        {
            LOG_INFO("no next behavior, return SUCCESS");
            return MoveBehaviorStatus::SUCCESS;
        }
        else 
        {
            LOG_INFO("next behavior, return NEXT_BEHAVIOR, type: {}", static_cast<int>(success_behavior_type));
            next_behavior_type_ = success_behavior_type;
            return MoveBehaviorStatus::NEXT_BEHAVIOR;
        }
    }
    else if (move_status == MoveStatus::RUNNING)
    {
        return MoveBehaviorStatus::RUNNING;
    }
    LOG_INFO("move status return INVALID");
    return MoveBehaviorStatus::INVALID;
}

BasicMove::MoveStatus BasicMove::ProcessMove(const Pose2f& pose)
{
    bool is_distance_arrival = false;
    bool is_time_arrival = false;
    if (config_.check_distance)
    {
        float dist = std::hypot(pose.x - start_pose_.x, pose.y - start_pose_.y);
        if (dist > config_.move_distance) 
        {
            is_distance_arrival = true;
        }
    }
    if (config_.check_time)
    {
        uint64_t current_time = GetSteadyClockTimestampMs();
        if (current_time - start_time_ > config_.move_time)
        {
            is_time_arrival = true;
        }
    }
    if (is_distance_arrival || is_time_arrival)
    {
        return MoveStatus::SUCCESS;
    }
    velocity_.linear = config_.linear;
    velocity_.angular = config_.angular;
    return MoveStatus::RUNNING;
}

BasicMove::MoveStatus BasicMove::ProcessTurn(const Pose2f& pose)
{
    bool is_angle_arrival = false;
    bool is_time_arrival = false;
    if (config_.check_angle)
    {
        float angle = NormalizeAngle(pose.theta - start_pose_.theta);
        if (std::abs(angle) > std::abs(turn_angle_))
        {
            is_angle_arrival = true;
        }
    }
    if (config_.check_time)
    {
        uint64_t current_time = GetSteadyClockTimestampMs();
        if (current_time - start_time_ > turn_duration_ms_)
        {
            is_time_arrival = true;
        }
    }
    if (is_angle_arrival || is_time_arrival)
    {
        return MoveStatus::SUCCESS;
    }
    velocity_.linear = 0;
    velocity_.angular = std::abs(config_.angular);
    if (turn_angle_ < 0)
    {
        velocity_.angular = -velocity_.angular;
    }
    return MoveStatus::RUNNING;
}

float BasicMove::GetTurnAngle(const PerceptionFusionResult &fusion_result) const
{
    const auto &obstacle = fusion_result.obstacle_result;

    if (obstacle.left_obstacle_status == ObstacleDetectStatus::HAVE_OBSTACLE)
    {
        LOG_DEBUG("[Collision] Obstacle detected on the left, turning right");
        return -config_.default_turn_angle; // Turn right
    }
    if (obstacle.right_obstacle_status == ObstacleDetectStatus::HAVE_OBSTACLE)
    {
        LOG_DEBUG("[Collision] Obstacle detected on the right, turning left");
        return config_.default_turn_angle; // Turn left
    }

    const bool random_turn = (rand() % 2 == 0); // 50% probability
    LOG_DEBUG("[Collision] No clear obstacle information, random turning direction: %s", random_turn ? "Right" : "Left");
    return random_turn ? -config_.default_turn_angle : config_.default_turn_angle;
}

}