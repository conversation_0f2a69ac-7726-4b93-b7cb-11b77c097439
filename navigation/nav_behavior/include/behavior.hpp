#ifndef NAVIGATION_BEHAVIOR_HPP
#define NAVIGATION_BEHAVIOR_HPP

#include "basic_move.hpp"
#include "data_type.hpp"
#include "ob_mower_msgs/nav_alg_ctrl__struct.h"
#include "ob_mower_msgs/nav_function_state.h"
#include "ob_mower_msgs/perception_fusion_result_struct.h"
#include "opencv2/opencv.hpp"
#include "path_track.hpp"
#include "predict_trajectory.hpp"
#include "velocity_publisher.hpp"
#include "velocity_smooth.hpp"

#include <atomic>
#include <chrono>
#include <cmath>
#include <memory>
#include <queue>
#include <sys/prctl.h>
#include <thread>
#include <unordered_map>

namespace fescue_iox
{

struct BehaviorAlgParam
{
    /****************************************Collision Recovery****************************************************/
    float collision_backup_speed = 0.1;     // Backup speed (m/s)/*param*/
    float collision_backup_duration = 2500; // Backup duration (ms)/*param*/
    float collision_turn_angle = M_PI / 6;  // Turn angle (30 degrees)/*param*/
    float collision_turn_speed = 0.3;       // Turn speed (rad/s)/*param*/

    /****************************************Lift Recovery****************************************************/
    float lift_backup_speed = 0.1;   // Backup speed during recovery phase (m/s)/*param*/
    int lift_backup_duration = 1500; // Backup duration during recovery phase (ms)/*param*/

    /****************************************Unstuck Recovery****************************************************/
    float backup_speed = 0.1;         // Backup speed (m/s)/*param*/
    float turn_angle = M_PI / 4;      // Rotation angle (45 degrees)/*param*/
    float forward_speed = 0.2;        // Forward speed (m/s)/*param*/
    uint64_t backup_duration = 1000;  // Backup duration (ms)/*param*/
    uint64_t turn_duration = 1000;    // Rotation duration (ms)/*param*/
    uint64_t forward_duration = 1000; // Forward duration (ms)/*param*/

    /****************************************Wheel Slip and Stall Detection and Recovery****************************************************/
    float wheel_radius = 0.1f;            // Wheel radius (meters)
    float slip_ratio_threshold = 0.3f;    // Slip ratio threshold (30%)
    float stall_current_threshold = 5.0f; // Stall current threshold (Amperes)
    float stall_speed_threshold = 0.1f;   // Stall speed threshold (m/s)
    float min_valid_speed = 0.05f;        // Minimum valid speed (m/s)
    float slip_reduce_ratio = 0.5f;       // Slip reduction ratio
    int max_stall_retries = 3;            // Maximum stall retry attempts
};

enum class BehaviorStatus : int
{
    InProgress = 0,
    Successed = 1,
    Failed = 2
};

struct BehaviorAlgResult
{
    bool behavior_completed{false};
    BehaviorStatus behavior_status{BehaviorStatus::InProgress};
    BehaviorAlgResult() = default;

    BehaviorAlgResult(bool behavior_completed)
        : behavior_completed(behavior_completed)
    {
    }

    BehaviorAlgResult(bool behavior_completed, BehaviorStatus status)
        : behavior_completed(behavior_completed)
        , behavior_status(status)
    {
    }
};

struct BehaviorExceptionInfo
{
    uint64_t time_ms = 0;
    bool is_slip = false;
    bool is_collision = false;
    bool is_lifting = false;
    Pose2f pose;
};

class NavigationBehaviorAlg
{
public:
    NavigationBehaviorAlg(const BehaviorAlgParam &param);
    ~NavigationBehaviorAlg();

    void SetBehaviorAlgParam(const BehaviorAlgParam &param);
    void GetBehaviorAlgParam(BehaviorAlgParam &param);
    void SetAlgoRunningState(MowerRunningState state);
    void ProhibitVelPublisher();
    void ResetBehaviorFlags();
    void SetBehaviorRunningStateCallback(std::function<void(BehaviorRunningState, bool, const std::vector<BehaviorExceptionType>&)> callback);

    // New
    BehaviorAlgResult DoBehavior(PerceptionFusionResult &fusion_result,
                                 McuExceptionStatus &mcu_exception_status,
                                 const ImuData &imu_data,
                                 const MotorSpeedData &motor_speed_data,
                                 const MotionDetectionResult &motion_detection_result,
                                 const std::vector<BehaviorExceptionType> &triggered_exception_types);

    void SetCollisionStatus(const McuExceptionStatus &mcu_exception_status);
    void SetLiftedStatus(const McuExceptionStatus &mcu_exception_status);
    void SetFunctionState(const fescue_msgs_enum_FunctionState &function_state);
    void ResetStatus();
    MowerRunningState GetMowerRunningState() const;

    void SetVelPublisherProhibit(bool prohibit)
    {
        if (vel_publisher_)
        {
            vel_publisher_->SetProhibitFlag(prohibit);
        }
    }
    void SetMotorSpeedData(const MotorSpeedData &motor_speed_data);
    void SetMotionDetectionResult(const MotionDetectionResult &motion_detection_result);
    void SetFusionPose(const ob_mower_msgs::NavFusionPose &fusion_pose);

    const char *GetVersion();

private:
    void DealFeatureSelect(ThreadControl control, bool state);
    void UpdateBehaviorRunningState(BehaviorRunningState state, bool is_exception_loop, const std::vector<BehaviorExceptionType>& exception_types);

    void ShowBehaviorPrint(BehaviorRunningState &behavior_state);

private:
    void PublishVelocity(float linear, float angular);
    void PauseVelocity();
    void ResumeVelocity();
    void UpdateTriggeredExceptionTypes(const BehaviorException &exception);

private:
    float GetTurnAngle(const PerceptionFusionResult &fusion_result);
    void UpdateExceptionStatus(const std::vector<BehaviorExceptionType> &triggered_exception_types);
    void InitMoveBehavior();
    void UpdateExceptionLoop(bool is_slip, bool is_collision, bool is_lifting, const Pose2f &pose);
    MoveBehaviorStatus ProcessMoveBehavior(const Pose2f &pose, const BehaviorException &exception,
                                           const PerceptionFusionResult &fusion_result);

private:
    std::function<void(BehaviorRunningState, bool, const std::vector<BehaviorExceptionType>&)> behavior_running_state_callback_;

    std::unique_ptr<VelocityPublisher> vel_publisher_{nullptr}; // Velocity publisher

    // New running status variables
    std::atomic<MowerRunningState> mower_running_state_{MowerRunningState::STOP}; // 0-Not started, 1-Running, 2-Paused
    BehaviorRunningState behavior_status_{BehaviorRunningState::UNDEFINED};

    bool is_collision_detected_ = false; // Collision detection flag

    float collision_backup_speed_ = 0.1;     // Backup speed (m/s)/*param*/
    float collision_backup_duration_ = 2500; // Backup duration (ms)/*param*/
    float collision_backup_angular_speed_ = 0.1;
    float collision_turn_angle_ = M_PI / 6; // Turn angle (30 degrees)/*param*/
    float collision_turn_speed_ = 0.3;      // Turn speed (rad/s)/*param*/
    float turn_speed_ = 0;
    float turn_duration_ms_ = 0;

    /****************************************Lift Recovery****************************************************/
    bool is_lifted_ = false; // Lift detection flag

    float lift_backup_speed_ = 0.1; // Backup speed during recovery phase (m/s)/*param*/
    float lift_backup_angular_speed_ = 0.1;
    int lift_backup_duration_ = 1500; // Backup duration during recovery phase (ms)/*param*/

    float backup_speed_ = 0.1;           // Backup speed (m/s)/*param*/
    float turn_angle_ = M_PI / 4;        // Rotation angle (45 degrees)/*param*/
    float forward_speed_ = 0.2;          // Forward speed (m/s)/*param*/
    uint64_t backup_duration_ = 1000;    // Backup duration (ms)/*param*/
    uint64_t turn_duration_ = 1000;      // Rotation duration (ms)/*param*/
    uint64_t forward_duration_ = 1000;   // Forward duration (ms)/*param*/

    std::mutex motor_speed_mtx_;
    MotorSpeedData motor_speed_data_;

    std::mutex motion_detection_result_mtx_;
    MotionDetectionResult motion_detection_result_;

    std::thread slip_detection_thread_;
    std::atomic_bool slip_detection_running_{false};
    std::atomic_bool is_slipping_detected_{false};
    // int slip_detection_frequency_{20}; // Detection frequency (Hz)

    float slipping_backup_speed_ = 0.15;    // Backup speed (m/s)/*param*/
    float slipping_backup_duration_ = 2500; // Backup duration (ms)/*param*/
    float slipping_turn_angle_ = M_PI / 6;  // Turn angle (30 degrees)/*param*/
    float slipping_turn_speed_ = 0.3;       // Turn speed (rad/s)/*param*/

    float wheel_radius_ = 0.1f; // Wheel radius (meters)
    float wheel_base_ = 0.335f;
    float slip_ratio_threshold_ = 0.3f;    // Slip ratio threshold (30%)
    float stall_current_threshold_ = 5.0f; // Stall current threshold (Amperes)
    float stall_speed_threshold_ = 0.1f;   // Stall speed threshold (m/s)
    float min_valid_speed_ = 0.05f;        // Minimum valid speed (m/s)

    std::mutex fusion_pose_mtx_;
    ob_mower_msgs::NavFusionPose fusion_pose_;

    std::deque<BehaviorExceptionInfo> behavior_exception_info_queue_;
    bool is_exception_loop_ = false;

    std::mutex function_state_mtx_;
    fescue_msgs_enum_FunctionState function_state_{fescue_msgs_enum_FunctionState::FUNCTION_STATE_INVALID};

    std::shared_ptr<BasicMove> cur_move_behavior_{nullptr};
    std::unordered_map<MoveBehaviorType, std::shared_ptr<BasicMove>> move_behavior_map_;
    bool is_error_ = false;
    bool is_stuck_ = false;
    std::vector<BehaviorExceptionType> triggered_exception_types_;

    /****************************************Wheel Slip and Stall Detection and Recovery****************************************************/
};

} // namespace fescue_iox

#endif
