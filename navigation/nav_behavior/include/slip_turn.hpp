#pragma once

#include "data_type.hpp"
#include "basic_move.hpp"

namespace fescue_iox
{

class SlipTurn : public BasicMove
{
public:
    SlipTurn(const MoveConfig& config) : BasicMove(config) 
    {
        SetSuccessBehaviorType(MoveBehaviorType::INVALID);
    }

    std::string GetName() const override { return "SLIP_TURN"; }

    MoveBehaviorType ProcessException(const BehaviorException& exception) override 
    {
        (void)exception;
        return MoveBehaviorType::INVALID;
    }

protected:
    void InitData(const PerceptionFusionResult &fusion_result, const AvoidObsMode& avoid_obs_mode) override 
    {
        (void)avoid_obs_mode;
        if (is_data_inited_) 
        {
            return;
        }
        is_data_inited_ = true;
        if (is_exception_loop_ && avoid_obs_mode == AvoidObsMode::AVOID_RIGHT_SIDE)
        {
            turn_angle_ = M_PI / 4;
        }
        else 
        {
            // 计算转向角度
            turn_angle_ = GetTurnAngle(fusion_result);
            if (std::abs(turn_angle_) > std::abs(config_.move_angle))
            {
                if (turn_angle_ > 0) 
                {
                    turn_angle_ = config_.move_angle;
                }
                else
                {
                    turn_angle_ = -config_.move_angle;
                }
            }
        }
        turn_duration_ms_ = std::abs(turn_angle_) / std::abs(config_.angular) * 1000;
        LOG_INFO("turn_angle: {}, turn_duration_ms: {} is_exception_loop_: {} avoid_obs_mode: {}", 
                 turn_angle_, turn_duration_ms_, is_exception_loop_, static_cast<int>(avoid_obs_mode));
    }
};

}