#pragma once

#include "iceoryx_hoofs/cxx/string.hpp"
#include "iceoryx_hoofs/cxx/vector.hpp"

#include <cstdint>
#include <string>

#define MAX_NAVIGATION_ALGO_NUM 16 /* 设置的能控制的算法的最多数量 */
#define MAX_BEHAVIOR_EXCEPTION_NUM 10

/**
 * @brief 算法类型ID
 */
enum fescue_msgs__enum__NavigationAlgoType
{
    /* 规控沿边算法 */
    FESCUE_MSGS_ENUM_NAV_ALGO_TYPE_EDGE_FOLLOW = 0,
    /* 规控跨区域算法 */
    FESCUE_MSGS_ENUM_NAV_ALGO_TYPE_CROSS_REGION = 1,
    /* 规控回充算法 */
    FESCUE_MSGS_ENUM_NAV_ALGO_TYPE_RECHARGE = 2,
    /* 规控随机割草算法 */
    FESCUE_MSGS_ENUM_NAV_ALGO_TYPE_RANDOM_MOWER = 3,
    /* 规控行为恢复算法 */
    FESCUE_MSGS_ENUM_NAV_ALGO_TYPE_BEHAVIOR = 4,
    /* 规控螺旋割草算法 */
    FESCUE_MSGS_ENUM_NAV_ALGO_TYPE_SPIRAL_MOWER = 5,
    /* 规控切边割草算法 */
    FESCUE_MSGS_ENUM_NAV_ALGO_TYPE_CUT_BORDER = 6,
    /* 规控脱困算法 */
    FESCUE_MSGS_ENUM_NAV_ALGO_TYPE_ESCAPE = 7,
    /* 全部算法 */
    FESCUE_MSGS_ENUM_NAV_ALGO_TYPE_ALL,
    FESCUE_MSGS_ENUM_NAV_ALGO_TYPE_UNKNOWN,
};

inline const std::string NavigationAlgoTypeToString(fescue_msgs__enum__NavigationAlgoType alg_type)
{
    if (alg_type == FESCUE_MSGS_ENUM_NAV_ALGO_TYPE_EDGE_FOLLOW)
    {
        return "edge_follow";
    }
    else if (alg_type == FESCUE_MSGS_ENUM_NAV_ALGO_TYPE_CROSS_REGION)
    {
        return "cross_region";
    }
    else if (alg_type == FESCUE_MSGS_ENUM_NAV_ALGO_TYPE_RECHARGE)
    {
        return "recharge";
    }
    else if (alg_type == FESCUE_MSGS_ENUM_NAV_ALGO_TYPE_RANDOM_MOWER)
    {
        return "random_mower";
    }
    else if (alg_type == FESCUE_MSGS_ENUM_NAV_ALGO_TYPE_BEHAVIOR)
    {
        return "behavior";
    }
    else if (alg_type == FESCUE_MSGS_ENUM_NAV_ALGO_TYPE_SPIRAL_MOWER)
    {
        return "spiral_mower";
    }
    else if (alg_type == FESCUE_MSGS_ENUM_NAV_ALGO_TYPE_CUT_BORDER)
    {
        return "cut_border";
    }
    else if (alg_type == FESCUE_MSGS_ENUM_NAV_ALGO_TYPE_ESCAPE)
    {
        return "escape";
    }
    else if (alg_type == FESCUE_MSGS_ENUM_NAV_ALGO_TYPE_ALL)
    {
        return "all";
    }
    else
    {
        return "unknown";
    }
}

/**
 * @brief 算法类型ID
 */
enum fescue_msgs__enum__NavigationAlgoState
{
    FESCUE_MSGS_ENUM_NAV_ALGO_STATE_IGNORE = -1,
    FESCUE_MSGS_ENUM_NAV_ALGO_STATE_DISABLE = 0,
    FESCUE_MSGS_ENUM_NAV_ALGO_STATE_ENABLE = 1,
};

inline const std::string NavigationAlgoStateToString(fescue_msgs__enum__NavigationAlgoState alg_state)
{
    if (alg_state == FESCUE_MSGS_ENUM_NAV_ALGO_STATE_IGNORE)
    {
        return "ignore";
    }
    else if (alg_state == FESCUE_MSGS_ENUM_NAV_ALGO_STATE_DISABLE)
    {
        return "disable";
    }
    else if (alg_state == FESCUE_MSGS_ENUM_NAV_ALGO_STATE_ENABLE)
    {
        return "enable";
    }
    else
    {
        return "unknown";
    }
}

enum fescue_msgs__enum__BehaviorExceptionType
{
    EXCEPTION_TYPE_SLIP = 0,
    EXCEPTION_TYPE_COLLISION = 1,
    EXCEPTION_TYPE_LIFTING = 2,
    EXCEPTION_TYPE_STUCK = 3,
};

/**
 * @brief 规控算法控制结构体类型
 */
struct fescue_msgs__msg__NavigationAlgoCtrlInfo
{
    fescue_msgs__enum__NavigationAlgoType type{FESCUE_MSGS_ENUM_NAV_ALGO_TYPE_UNKNOWN};   /* 算法类型 */
    fescue_msgs__enum__NavigationAlgoState state{FESCUE_MSGS_ENUM_NAV_ALGO_STATE_IGNORE}; /* 控制状态，-1, 忽略，0-关闭，1-开启 */
    iox::vector<fescue_msgs__enum__BehaviorExceptionType, MAX_BEHAVIOR_EXCEPTION_NUM> behavior_exception_types;
    bool is_recover_from_exception{false};
    bool is_behavior_loop{false};
    iox::vector<fescue_msgs__enum__BehaviorExceptionType, MAX_BEHAVIOR_EXCEPTION_NUM> last_triggered_exception_types;

    bool operator==(const fescue_msgs__msg__NavigationAlgoCtrlInfo &other) const
    {
        if (type != other.type || state != other.state)
            return false;

        if (is_recover_from_exception != other.is_recover_from_exception ||
            is_behavior_loop != other.is_behavior_loop)
            return false;

        if (behavior_exception_types.size() != other.behavior_exception_types.size())
            return false;

        for (size_t i = 0; i < behavior_exception_types.size(); ++i)
        {
            if (behavior_exception_types[i] != other.behavior_exception_types[i])
                return false;
        }

        if (last_triggered_exception_types.size() != other.last_triggered_exception_types.size())
            return false;
        for (size_t i = 0; i < last_triggered_exception_types.size(); ++i)
        {
            if (last_triggered_exception_types[i] != other.last_triggered_exception_types[i])
                return false;
        }

        return true;
    }

    bool operator!=(const fescue_msgs__msg__NavigationAlgoCtrlInfo &other) const
    {
        return !(*this == other);
    }
};

/**
 * @brief 规控算法控制数据
 */
struct fescue_msgs__msg__NavigationAlgoCtrlData
{
    iox::string<64> sender;
    iox::vector<fescue_msgs__msg__NavigationAlgoCtrlInfo, MAX_NAVIGATION_ALGO_NUM> data;

    bool operator==(const fescue_msgs__msg__NavigationAlgoCtrlData &other) const
    {
        if (sender != other.sender)
            return false;

        if (data.size() != other.data.size())
            return false;

        for (size_t i = 0; i < data.size(); ++i)
        {
            if (data[i] != other.data[i])
                return false;
        }

        return true;
    }

    bool operator!=(const fescue_msgs__msg__NavigationAlgoCtrlData &other) const
    {
        return !(*this == other);
    }
};

struct fescue_msgs__srv__NavigationAlgoCtrl_Request
{
    iox::vector<fescue_msgs__msg__NavigationAlgoCtrlInfo, MAX_NAVIGATION_ALGO_NUM> data;
};

struct fescue_msgs__srv__NavigationAlgoCtrl_Response
{
    bool success;
    iox::string<32> message;
};
